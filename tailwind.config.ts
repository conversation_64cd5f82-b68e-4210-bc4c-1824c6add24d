import type { Config } from "tailwindcss";

const config: Config = {
  // darkMode: "class",
  content: [
    "./app/**/*.{js,ts,jsx,tsx}",   // For App Router
    "./pages/**/*.{js,ts,jsx,tsx}", // For Page Router (optional)
    "./components/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      screens: {
        "3xl": "1900px",
      },
      fontFamily: {
        metropolis: ["var(--font-metropolis)", "sans-serif"],
      },
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic":
          "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
      },
      colors: {
        warning: "#B91C1C",
        primary: "#00A2B0",
        primarybutton: "#00A2B0",
        primaryhover: "#80D1D7",

        secondary: "#172B3E",
        secondarybutton: "#0C4B5E",
        secondaryhover: "#85A5AE",

        success: "#A7F3D0",

        neutrals: "#FDFDFD",
        neutralsdark: "#201A1B",
        neutralsrichGrey: "#737373",
        neutralsmildGrey: "#D9D9D9",

        lightTeal: "#55AA81",
        lightTealHover: "#AAD5C0",
        lightTealBg: "#E3FFF0",

        mutedTeal: "#4A8C91",
        mutedTealHover: "#A4C5C8",
        mutedTealBg: "#DCFCF9",

        blueTeal: "#00A2B0",
        blueTealHover: "#80D1D7",
        blueTealBg: "#EBFDFF",

        oceanBlue: "#2D75AE",
        oceanBlueHover: "#96BAD7",
        oceanBlueBg: "#F0F9FF",

        blueViolet: "#374788",
        blueVioletHover: "#9BA3C3",
        blueVioletBg: "#8E98A8",

        midnightBlue: "#172B3E",
        midnightBlueHover: "#8B959E",
        midnightBlueBg: "#EAF5FF",

        deepTeal: "#0C4B5E",
        deepTealHover: "#85A5AE",
        deepTealBg: "#E2F8FF",
      },
      textColor: {
        body: "#666666",
        "body-dark": "#333333",
        muted: "#9ca3af",
        "muted-light": "#d1d5db",
        heading: "#1f2937",
        "sub-heading": "#374151",
        bolder: "#1f2937",
      },
      height: {
        13: "3.125rem",
        double: "200%",
      },
      maxWidth: {
        5: "1.25rem",
      },
      maxHeight: {
        5: "1.25rem",
      },
      spacing: {
        22: "5.5rem",
      },
      borderRadius: {
        DEFAULT: "5px",
      },
      boxShadow: {
        base: "rgba(0, 0, 0, 0.16) 0px 4px 16px",
        translatePanel: "0px 15px 50px rgba(71, 92, 111, 0.15)",
        chatBox:
          "0px 1px 3px rgba(0, 0, 0, 0.1), 0px 1px 2px -1px rgba(0, 0, 0, 0.1)",
        cardAction:
          "0 0 0 1px #8898aa1a, 0 15px 35px #31315d1a, 0 5px 15px #00000014",
        chat: "0px 1px 2px rgba(0, 0, 0, 0.08)",
        box: "0 10px 15px -3px rgb(0 0 0 / 0.05), 0 4px 6px -4px rgb(0 0 0 / 0.05)",
        promptSuggestion: "0px 2px 6px rgba(59, 74, 92, 0.1)",
        avatar: "0px 1px 2px rgba(0, 0, 0, 0.12)",
        shopLogo: "2px 4px 16px rgba(0, 0, 0, 0.08)",
        shopTransferCard: "0px 2px 6px 0px rgba(75, 85, 99, 0.04)",
        shopTransferTable: "0px 2px 4px 0px rgba(75, 85, 99, 0.05)",
      },
      dropShadow: {
        shopLogo: "2px 4px 16px rgba(0, 0, 0, 0.08)",
      },
      gridTemplateColumns: {
        fit: "repeat(auto-fit, minmax(0, 1fr))",
      },
      fontSize: {
        h1: ["36px", { lineHeight: "40px" }],
        h2: ["30px", { lineHeight: "36px" }],
        h3: ["24px", { lineHeight: "32px" }],
        h4: ["20px", { lineHeight: "28px" }],
        h5: ["18px", { lineHeight: "26px" }],
        h6: ["16px", { lineHeight: "24px" }],
        b1: ["24px", { lineHeight: "32px" }],
        b2: ["20px", { lineHeight: "28px" }],
        b3: ["18px", { lineHeight: "26px" }],
        b4: ["16px", { lineHeight: "24px" }],
        b5: ["14px", { lineHeight: "20px" }],
        b6: ["12px", { lineHeight: "16px" }],
      },
      fontWeight: {
        regular: "400",
        medium: "500",
        semibold: "600",
        bold: "700",
      },
    },
  },
  plugins: [
    require("@tailwindcss/typography"),
    require("@tailwindcss/forms"),
  ],
};

export default config;