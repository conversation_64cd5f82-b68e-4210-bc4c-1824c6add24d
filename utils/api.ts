import { useState } from 'react';
import HTTPClient from './http-client';
import API_ENDPOINTS from './api-endpoints';
import { SignInInputFormData } from '@/schemas/auth/signin/schema';
import { SignInResponse } from '@/data/signin';
import { RegisterResponse } from '@/data/register';
import { RegisterInputFormData } from '@/schemas/auth/register/schema';
import { RenewTokenResponse } from '@/data/newtoken';
import { VerifyTokenResponse } from '@/data/verifytoken';
import { ForgotUsernameFormData } from '@/schemas/auth/forgot-username/schema';
import { ForgotPasswordFormData } from '@/schemas/auth/forgot-password/schema';
import { ForgotPasswordResponse } from '@/data/forgot-password';
import { UpdatePasswordRequest, UpdatePasswordResponse } from '@/data/update-password';
import { ChangePasswordFormData } from '@/schemas/auth/change-password/schema';
import { InviteFormData } from '@/schemas/auth/invite/schema';
import { InviteResponse } from '@/data/send-invite';
import { ResendInviteFormData } from '@/schemas/api/resend-invite/schema';
import { ResendInviteResponse } from '@/data/resend-invite';
import { InviteAcceptanceFormData } from '@/schemas/auth/invite-acceptance/schema';
import { DeleteUserResponse, GetUsersByPageResponse, GetUsersResponse, UpdateUserRequest, UpdateUserResponse, UserDetailResponse } from '@/data/user';
import { GetPermissionsResponse } from '@/data/permissions';
import { GetRolesResponse } from '@/data/roles';
import { GetCustomersResponse } from '@/data/customer';
import { GetStatsResponse, Stat } from '@/data/stat';
import { StatsFormData } from '@/schemas/api/stats/schema';
import { ContactFormData } from '@/schemas/api/contact/schema';
import { AddContactResponse } from '@/data/contact';
import { AddFeedbackResponse, GetFeedbackResponse } from '@/data/feedback';
import { FeedbackFormData } from '@/schemas/api/feedback/schema';
import { TokenCheckResponse } from '@/data/token-check';
import { ProfileResponse } from '@/data/profile';
import Cookies from 'js-cookie';
const http = new HTTPClient();

export const Login =  async (credentials: SignInInputFormData): Promise<APIResponse<SignInResponse>>  => {
  const response = await http.post<APIResponse>(
    API_ENDPOINTS.SIGNIN,
    credentials
  );
  return response.data;
};

export const Logout = async (): Promise<APIResponse<[]>> => {
  const response = await http.post<APIResponse<[]>>(API_ENDPOINTS.LOGOUT);
  return response.data;
};

export const ForceLogout = async (): Promise<APIResponse<null>> => {
  const response = await http.post<APIResponse<null>>(API_ENDPOINTS.FORCE_LOGOUT);
  return response.data;
};


export const Register = async (
  body: RegisterInputFormData
): Promise<APIResponse<RegisterResponse>> => {
  const response = await http.post<APIResponse<RegisterResponse>>(
    API_ENDPOINTS.REGISTER,
    body
  );
  return response.data;
};


export const RenewToken = async (
  body: { refresh_token: string }
): Promise<APIResponse<RenewTokenResponse>> => {
  const response = await http.post<APIResponse<RenewTokenResponse>>(
    API_ENDPOINTS.RENEW_TOKEN,
    body
  );
  return response.data;
};

export const VerifyToken = async (
  body: { refresh_token: string }
): Promise<APIResponse<VerifyTokenResponse>> => {
  const response = await http.post<APIResponse<VerifyTokenResponse>>(
    API_ENDPOINTS.VERIFY_TOKEN,
    body
  );
  return response.data;
};

export const ForgotUsername = async (
  body: ForgotUsernameFormData
): Promise<APIResponse<string>> => {
  const response = await http.post<APIResponse<string>>(
    API_ENDPOINTS.FORGOT_USERNAME,
    body
  );
  return response.data;
};

export const ForgotPassword = async (
  body: ForgotPasswordFormData
): Promise<APIResponse<ForgotPasswordResponse>> => {
  const response = await http.post<APIResponse<ForgotPasswordResponse>>(
    API_ENDPOINTS.FORGOT_PASSWORD,
    body
  );
  return response.data;
};

export const ResetPassword = async (
  body: ChangePasswordFormData
): Promise<APIResponse<UpdatePasswordResponse>> => {
  const response = await http.post<APIResponse<UpdatePasswordResponse>>(
    API_ENDPOINTS.RESET_PASSWORD,
    body
  );
  return response.data;
};

export const SendInvite = async (
  body: InviteFormData
): Promise<APIResponse<InviteResponse>> => {
  const response = await http.post<APIResponse<InviteResponse>>(
    API_ENDPOINTS.INVITE_SEND,
    body
  );
  return response.data;
};

export const ResendInvite = async (
  body: ResendInviteFormData
): Promise<APIResponse<ResendInviteResponse>> => {
  const response = await http.post<APIResponse<ResendInviteResponse>>(
    API_ENDPOINTS.INVITE_RESEND,
    body
  );
  return response.data;
};

export const AcceptInvite = async (
  body: InviteAcceptanceFormData & { token: string }
): Promise<APIResponse<null>> => {
  const response = await http.post<APIResponse<null>>(
    API_ENDPOINTS.INVITE_ACCEPT,
    body
  );
  return response.data;
};

export const GetUsers = async (): Promise<APIResponse<GetUsersResponse>> => {
  const response = await http.post<APIResponse<GetUsersResponse>>(
    API_ENDPOINTS.USERS
  );
  return response.data;
};

export const GetUsersByPage = async (
  page: number,
  count: number = 10
): Promise<APIResponse<GetUsersByPageResponse>> => {
  const response = await http.post<APIResponse<GetUsersByPageResponse>>(
    API_ENDPOINTS.USERS_BY_PAGE(page),
    { count }
  );
  return response.data;
};

export const GetUserDetail = async (
  id: string
): Promise<APIResponse<UserDetailResponse>> => {
  const response = await http.post<APIResponse<UserDetailResponse>>(
    API_ENDPOINTS.USER_DETAIL(id)
  );
  return response.data;
};

export const DeleteUser = async (
  id: string
): Promise<APIResponse<DeleteUserResponse>> => {
  const response = await http.post<APIResponse<DeleteUserResponse>>(
    API_ENDPOINTS.USER_DELETE(id)
  );
  return response.data;
};

export const UpdateUser = async (
  id: string,
  body: UpdateUserRequest
): Promise<APIResponse<UpdateUserResponse>> => {
  const response = await http.post<APIResponse<UpdateUserResponse>>(
    API_ENDPOINTS.USER_UPDATE(id),
    body
  );
  return response.data;
};

export const GetPermissions = async (): Promise<APIResponse<GetPermissionsResponse>> => {
  const response = await http.post<APIResponse<GetPermissionsResponse>>(
    API_ENDPOINTS.PERMISSIONS
  );
  return response.data;
};

export const GetRoles = async (): Promise<APIResponse<GetRolesResponse>> => {
  const response = await http.post<APIResponse<GetRolesResponse>>(
    API_ENDPOINTS.ROLES
  );
  return response.data;
};

export const Customers = async (): Promise<APIResponse<GetCustomersResponse>> => {
  const response = await http.post<APIResponse<GetCustomersResponse>>(
    API_ENDPOINTS.CUSTOMERS
  );
  return response.data;
};

export const GetStats = async (): Promise<APIResponse<GetStatsResponse>> => {
  const response = await http.post<APIResponse>(
    API_ENDPOINTS.STATS
  );
  return response.data;
};

export const GetStatsByPage = async (
  page: number,
  count: number = 10
): Promise<APIResponse<GetStatsResponse>> => {
  const response = await http.post<APIResponse<GetStatsResponse>>(
    API_ENDPOINTS.STATS_BY_PAGE(page),
    { count }
  );
  return response.data;
};

export const AddStat = async (
  body: StatsFormData
): Promise<APIResponse<Stat>> => {
  const response = await http.post<APIResponse<Stat>>(
    API_ENDPOINTS.ADD_STATS,
    body
  );
  return response.data;
};


export const AddContact = async (
  body: ContactFormData
): Promise<APIResponse<AddContactResponse>> => {
  const response = await http.post<APIResponse<AddContactResponse>>(
    API_ENDPOINTS.ADD_CONTACT,
    body
  );
  return response.data;
};

export const GetFeedbacks = async (): Promise<APIResponse<GetFeedbackResponse>> => {
  const response = await http.post<APIResponse<GetFeedbackResponse>>(
    API_ENDPOINTS.FEEDBACKS
  );
  return response.data;
};

export const GetFeedbacksByPage = async (
  page: number,
  count: number = 10
): Promise<APIResponse<GetFeedbackResponse>> => {
  const response = await http.post<APIResponse<GetFeedbackResponse>>(
    API_ENDPOINTS.FEEDBACKS_BY_PAGE(page),
    { count }
  );
  return response.data;
};

export const AddFeedback = async (
  body: FeedbackFormData
): Promise<APIResponse<AddFeedbackResponse>> => {
  const response = await http.post<APIResponse<AddFeedbackResponse>>(
    API_ENDPOINTS.ADD_FEEDBACK,
    body
  );
  return response.data;
};

export const CheckTokenStatus = async (): Promise<TokenCheckResponse> => {
  const response = await http.get<TokenCheckResponse>(API_ENDPOINTS.TOKEN_CHECK);
  return response.data;
};

export const GetProfile = async (): Promise<APIResponse<ProfileResponse>> => {
  const response = await http.post<APIResponse<ProfileResponse>>(API_ENDPOINTS.PROFILE);
  return response.data;
};