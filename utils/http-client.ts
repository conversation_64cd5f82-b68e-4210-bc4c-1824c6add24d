import axios, {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
} from 'axios';
import { ACCESS_TOKEN, getCookie } from '@/lib/cookie-store';

class HTTPClient {
  private axiosInstance: AxiosInstance;

  constructor(timeout = 10000) {
    this.axiosInstance = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_URL, // Use NEXT_PUBLIC_ for client env access
      timeout,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  // Attach interceptors
  private setupInterceptors() {
    this.axiosInstance.interceptors.request.use(
      (config) => {
        const token = getCookie(ACCESS_TOKEN);
        console.log('tooken === ' + token);

        config.headers = config.headers || {};

        (config.headers as Record<string, string>)['Content-Type'] = 'application/json';
        (config.headers as Record<string, string>)['Accept'] = 'application/json';

        if (token) {
          (config.headers as Record<string, string>)['Authorization'] = `Bearer ${token}`;
        }

        return config;
      },
      (error) => Promise.reject(error)
    );

    this.axiosInstance.interceptors.response.use(
      (response) => response,
      (error) => {
        console.error('API Response Error:', error?.response || error?.message);
        return Promise.reject(error);
      }
    );
  }

  // POST request
  public async post<T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    try {
      return await this.axiosInstance.post<T>(url, data, config);
    } catch (error) {
      console.error(`POST ${url} failed:`, error);
      throw error;
    }
  }

  // Optional: Add GET, PUT, DELETE, etc.
  public async get<T>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    try {
      return await this.axiosInstance.get<T>(url, config);
    } catch (error) {
      console.error(`GET ${url} failed:`, error);
      throw error;
    }
  }
}

export default HTTPClient;