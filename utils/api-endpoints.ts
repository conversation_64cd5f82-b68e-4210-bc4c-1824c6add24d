const API_ENDPOINTS = {
  // ===== Auth =====
  REGISTER: '/v1/register',
  SIGNIN: '/v1/signin',
  LOGOUT: '/v1/logout',
  FORCE_LOGOUT: '/v1/logout/force',
  RENEW_TOKEN: '/v1/token/new',
  VERIFY_TOKEN: '/v1/token/verify',
  PROFILE: '/v1/profile',

  // ===== Password & Username Recovery =====
  FORGOT_USERNAME: '/v1/username/forgot',
  FORGOT_PASSWORD: '/v1/password/forgot',
  RESET_PASSWORD: '/v1/password/update',

  // ===== Invite =====
  INVITE_SEND: '/v1/invite/send',
  INVITE_RESEND: '/v1/invite/resend',
  INVITE_ACCEPT: '/v1/invite/accept',

  // ===== Users =====
  USERS: '/v1/users',
  USERS_BY_PAGE: (page: number) => `/v1/users/${page}`,
  USER_DETAIL: (id: string) => `/v1/user/${id}`,
  USER_UPDATE: (id: string) => `/v1/user/${id}/update`,
  USER_DELETE: (id: string) => `/v1/user/${id}/delete`,

  // ===== Customers =====
  CUSTOMERS: '/v1/customers',

  // ===== Roles & Permissions =====
  ROLES: '/v1/roles',
  PERMISSIONS: '/v1/permissions',

  // ===== Stats =====
  STATS: '/v1/stats',
  STATS_BY_PAGE: (page: number) => `/v1/stats/${page}`,
  ADD_STATS: '/v1/stats/add',

  // ===== Contacts =====
  CONTACTS: '/v1/contacts',
  CONTACTS_BY_PAGE: (page: number) => `/v1/contacts/${page}`,
  ADD_CONTACT: '/v1/contact/add',

  // ===== Feedback =====
  FEEDBACKS: '/v1/feedbacks',
  FEEDBACKS_BY_PAGE: (page: number) => `/v1/feedbacks/${page}`,
  ADD_FEEDBACK: '/v1/feedback/add',

  // ===== Utility =====
  TOKEN_CHECK: '/v1/protected',
};

  export default API_ENDPOINTS;