export type User = {
    id: number;
    name: string;
    email: string;
    username: string;
    picture: string | null;
    banner: string | null;
    created_at: string;
    updated_at: string;
  };

export type UpdateUserRequest = {
name?: string;
email?: string;
username?: string;
// optionally add: role_id, team_id, picture, etc.
};

export type UpdateUserResponse = User;
  
export type GetUsersResponse = User[];
export type GetUsersByPageResponse = User[];
export type UserDetailResponse = User; 
export type DeleteUserResponse = number; 
