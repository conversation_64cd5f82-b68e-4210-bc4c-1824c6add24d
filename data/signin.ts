export type SignInResponse = {
  user?: UserData;
  tokens?: TokensData;
  roles?: String[];
  permissions?: string[];
};

export type UserData = {
  id?: number;
  name?: string;
  email?: string;
  username?: string;
};

export type RolesData = {
  id?: number;
  name?: string;
  label?: string;
};

export type TokensData = {
  access_token?: string;
  refresh_token?: string;
  expires_in?: number;
};
