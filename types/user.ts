type UserDetailsResponse = {
    id: string,
    first_name?: string,
    last_name?: string,
    username?: string,
    email?: string,
    picture?: string,
    email_verified?: string,
    is_two_factor_enabled?: boolean,
    team: Team,
    role: Role,
    userToProduct?: [UserToProduct]
    userToBuilding?: [UserToBuilding]
}

type UserToProduct = {
    product?: Product
    customer?: Customer
    full_access?: boolean
}

type Product = {
    id?: any,
    name?: string,
}

type Customer = {
    id?: any,
    name?: string,
}

type UserToBuilding = {
    id?: any,
    building?: Buildingg,
}

type Buildingg = {
    id?: any,
    name?: string,
    organization?: Organization
}

type Organization = {
    id?: any,
    name?: string,
}

type Team = {
    id?: any,
    name?: string,
}


type Role = {
    id?: any,
    name?: string,
}