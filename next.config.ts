/** @type {import('next').NextConfig} */
const nextConfig = {
  trailingSlash: false,
  images:{
      remotePatterns:[
          {
              protocol: "https",
              hostname: "play-lh.googleusercontent.com",
          },
          {
              protocol: "https",
              hostname: "lifeguarddh.com",
          },
      ]
  },
  env: {
      NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
      NEXT_PUBLIC_LITE_URL: process.env.NEXT_PUBLIC_LITE_URL,
      NEXT_PUBLIC_ADMIN_URL: process.env.NEXT_PUBLIC_ADMIN_URL,
      NEXT_PUBLIC_CONNECT_URL: process.env.NEXT_PUBLIC_CONNECT_URL,
  },
  crossOrigin: "use-credentials"
};

export default nextConfig;
