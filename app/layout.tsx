import type { <PERSON>ada<PERSON> } from "next";
import './globals.css';
import { QueryProvider } from "@/providers/QueryProvider";

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="light">
      <body
        className="typography font-metropolis"
      >
        <QueryProvider>
          {children}
        </QueryProvider>
      </body>
    </html>
  );
}
