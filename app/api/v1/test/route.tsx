import { ResponseError, ResponseSuccess } from "@/lib/response";
import { BetaAnalyticsDataClient } from '@google-analytics/data';
import { NextRequest, NextResponse } from "next/server";
import { FirebaseSchema } from "@/schemas/api/firebase/schema";
import { fetchReport, mapAndroidToIos, combineResponses } from '@/lib/firebase-utils';

export const dynamic = 'force-dynamic';
// Handler for POST requests
export async function GET(req: NextRequest) {
    try {
      // Extract query parameters
      const { searchParams } = new URL(req.url);
      const startDate = searchParams.get('startDate');
      const endDate = searchParams.get('endDate');
      const eventName = searchParams.get('eventName') || '';
      const metricNames = searchParams.getAll('metricNames');
      const dimensionNames = searchParams.getAll('dimensionNames');
  
      // Validate the parameters against the FirebaseSchema (mocked as simple validation)
      const validatedFields = FirebaseSchema.safeParse({ startDate, endDate, eventName, metricNames, dimensionNames });
      if (!validatedFields.success) {
        const errors = validatedFields.error.errors.flatMap(err => err.message);
        return ResponseError("Invalid fields!", errors);
      }
  
      // Set dynamic default values
      const today = new Date();
      const defaultEndDate = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
      const defaultStartDate = new Date(today);
      defaultStartDate.setDate(today.getDate() - 30);
      const formattedDefaultStartDate = `${defaultStartDate.getFullYear()}-${String(defaultStartDate.getMonth() + 1).padStart(2, '0')}-${String(defaultStartDate.getDate()).padStart(2, '0')}`;
  
      // Use the provided dates or fall back to default values
      const finalStartDate = startDate || formattedDefaultStartDate;
      const finalEndDate = endDate || defaultEndDate;
  
      // Define pairs of events and metrics/dimensions to be fetched
      const eventMetricPairs = [
        { eventName, metricNames: ['eventCount', 'activeUsers', 'newUsers', 'averageSessionDuration'], filters: { region: 'British Columbia' } },
        //{ eventName, dimensionNames: ['region'] },
        //{ eventName, metricNames: ['eventCount', 'activeUsers'], dimensionNames: ['unifiedScreenClass'], filters: { region: 'British Columbia' }},
        //{ eventName, metricNames: ['eventCount', 'activeUsers'], dimensionNames: ['unifiedScreenName'],filters: { region: 'British Columbia' } },
      ];
  
      // Fetch data for each pair concurrently
      const responses = await Promise.all(
        eventMetricPairs.map((pair) => fetchReport({ ...pair, startDate: finalStartDate, endDate: finalEndDate }))
      );
  
      // Combine responses from all pairs
      const combinedResponse = combineResponses(responses, mapAndroidToIos);
  
      // Return a successful response with the combined data
      return ResponseSuccess('Data retrieved successfully', combinedResponse);
    } catch (error) {
      console.error("Error occurred:", error);
      return ResponseError('An error occurred while processing your request.');
    }
  }