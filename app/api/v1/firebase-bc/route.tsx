import { ResponseError, ResponseSuccess } from "@/lib/response";
import { BetaAnalyticsDataClient } from '@google-analytics/data';
import { NextRequest, NextResponse } from "next/server";
import { FirebaseSchema } from "@/schemas/api/firebase/schema";
import { fetchReport, mapAndroidToIos, combineResponses, setDataToZeroByPrefixes } from '@/lib/firebase-utils';

export const dynamic = 'force-dynamic';
// Handler for POST requests
export async function POST(req: NextRequest) {
  try {
    // Parse the JSON body of the request
    const body = await req.json();

    // Validate the body against the FirebaseSchema
    const validatedFields = FirebaseSchema.safeParse(body);
    if (!validatedFields.success) {
      // If validation fails, return an error response
      const errors = validatedFields.error.errors.flatMap(err => err.message);
      return ResponseError("Invalid fields!", errors);
    }

    // Destructure validated fields
    const { startDate, endDate, eventName = '', metricNames = ['eventCount'], dimensionNames = ['eventName'] } = validatedFields.data;

    // Set dynamic default values
    const today = new Date();
    const defaultEndDate = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
    const defaultStartDate = new Date(today);
    defaultStartDate.setDate(today.getDate() - 30);
    const formattedDefaultStartDate = `${defaultStartDate.getFullYear()}-${String(defaultStartDate.getMonth() + 1).padStart(2, '0')}-${String(defaultStartDate.getDate()).padStart(2, '0')}`;

    // Use the provided dates or fall back to default values
    const finalStartDate = startDate || formattedDefaultStartDate;
    const finalEndDate = endDate || defaultEndDate;

    // Define pairs of events and metrics/dimensions to be fetched
    const eventMetricPairs = [
      { eventName, metricNames: ['eventCount', 'activeUsers', 'newUsers', 'averageSessionDuration'] , filters: { region: 'British Columbia' } },
      { eventName, dimensionNames: ['region'] },
      { eventName, metricNames: ['eventCount', 'activeUsers'], dimensionNames: ['unifiedScreenClass'], filters: { region: 'British Columbia' } },
      { eventName, metricNames: ['eventCount', 'activeUsers'], dimensionNames: ['unifiedScreenName'] ,filters: { region: 'British Columbia' }},
    ];

    // Fetch data for each pair concurrently
    const responses = await Promise.all(
      eventMetricPairs.map((pair) => fetchReport({ ...pair, startDate: finalStartDate, endDate: finalEndDate }))
    );

    // Combine responses from all pairs
    const combinedResponse = combineResponses(responses, mapAndroidToIos);
    
    // Apply the setDataToZeroByPrefixes utility function to set "classic" and "metis" data to zero
    const finalResponse = setDataToZeroByPrefixes(combinedResponse, [ "metis","métis"]); // Pass your desired prefixes here


    // Return a successful response with the combined data
    return ResponseSuccess('Data retrieved successfully', finalResponse);
  } catch (error) {
    // If an error occurs, log it and return an error response
    console.error(error);
    return ResponseError('An error occurred while processing your request.');
  }
}

// Export the POST handler as default
// export { POST as default };