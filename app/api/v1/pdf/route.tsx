import { NextRequest, NextResponse } from 'next/server';
import puppeteer from 'puppeteer';
import { ResponseError, ResponseSuccess } from "@/lib/response";
import { ACCESS_TOKEN } from '@/lib/cookie-store';

export async function POST(req: NextRequest) {
  console.log('[PDF] POST handler triggered');

  const rawBody = await req.text();
  console.log('[PDF] Raw request body:', rawBody);

  try {
    let parsedBody;
    try {
      parsedBody = JSON.parse(rawBody);
    } catch (jsonError) {
      console.error('[PDF] JSON parse error:', jsonError);
      return ResponseError('Invalid JSON in request body.');
    }

    const { url: partialUrl, token: tokenFromBody, search } = parsedBody;
    console.log('[PDF] URL received:', partialUrl);
    console.log('[PDF] Permission search:', search);

    if (!partialUrl || typeof partialUrl !== 'string') {
      return ResponseError('Invalid URL provided');
    }

    const authHeader = req.headers.get('authorization');
    const token = tokenFromBody || authHeader?.replace('Bearer ', '');
    console.log('[PDF] Received token:', token);

    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || `http://${req.headers.get('host')}`;
    const fullUrl = new URL(partialUrl, baseUrl);

    if (search) {
      const searchParams = new URLSearchParams(search);
      for (const [key, value] of searchParams.entries()) {
        fullUrl.searchParams.set(key, value);
      }
    }

    if (token && !fullUrl.searchParams.has('token')) {
      fullUrl.searchParams.set('token', token);
    }

    console.log('[PDF] Final full URL:', fullUrl.toString());

    const browser = await puppeteer.launch({
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });

    const page = await browser.newPage();

    page.on('pageerror', err => console.error('[Page error]', err));

   
    if (token) {
      await page.setCookie({
        name: ACCESS_TOKEN, 
        value: token,
        domain: 'localhost', 
        path: '/',
        httpOnly: false,
        secure: baseUrl.startsWith('https'),
      });
      console.log('[PDF] Cookie set');
    }

    console.log('[PDF] Navigating to:', fullUrl.toString());

    const response = await page.goto(fullUrl.toString(), {
      waitUntil: 'domcontentloaded',
      timeout: 15000,
    });

    console.log('[PDF] Page loaded:', response?.status());
    await waitForPageToStabilize(page); 


    await new Promise(resolve => setTimeout(resolve, 1000));
    await page.emulateMediaType('screen');

    const bodyHandle = await page.$('body');
    const boundingBox = bodyHandle ? await bodyHandle.boundingBox() : null;
    const height = boundingBox ? boundingBox.height : 1080;
    await bodyHandle?.dispose();

    await page.setViewport({ width: 2560, height: Math.ceil(height) });

    const pdf = await page.pdf({
      format: 'A0',
      printBackground: true,
      landscape: true,
    });

    await browser.close();

    console.log('[PDF] PDF generated, size:', pdf.length);

    return new NextResponse(pdf, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Length': pdf.length.toString(),
        'Content-Disposition': 'attachment; filename="report.pdf"',
      },
    });
  } catch (error) {
    console.error('[PDF] Unexpected error:', error);
    return ResponseError('An error occurred while generating the PDF.');
  }
}

  async function waitForPageToStabilize(page: puppeteer.Page, maxAttempts = 10, delay = 500) {
  let previousHeight = await page.evaluate(() => document.body.scrollHeight);

  for (let i = 0; i < maxAttempts; i++) {
    await new Promise(resolve => setTimeout(resolve, delay));

    const currentHeight = await page.evaluate(() => document.body.scrollHeight);

    if (currentHeight === previousHeight) {
      console.log(`[PDF] Page height stabilized at ${currentHeight}px`);
      return true;
    }

    previousHeight = currentHeight;
  }

  console.warn('[PDF] Page did not stabilize in time');
  return false;
}

