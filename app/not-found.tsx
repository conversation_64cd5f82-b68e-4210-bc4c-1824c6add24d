'use client'
import React from "react";
import Image from "next/image";
import Logo from "../components/ui/logo/logo";
import { ChevronLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { Button } from "../components/ui/buttons/button";

const ErrorPage = () => {
    var router = useRouter()
    return ( 
        <div className="w-full h-screen bg-white text-black bg-gradient-to-r from-[#EBFDFF]/80 to-[#80D1D7]/50">
            <div className="p-10 xl:mx-5">
                <Logo />
            </div>

            <div className="flex-grow flex flex-col justify-center items-center text-center px-4">
                <div className="w-full max-w-[600px]">
                <Image
                    src="/404.svg"
                    alt="404 Illustration"
                    width={0}
                    height={0}
                    sizes="100vw"
                    className="w-full h-auto"
                    priority
                />
                </div>

                <div className="space-y-4 max-w-4xl">
                    <h1 className="text-primary text-3xl font-bold">
                        Sorry, the page can’t be found
                    </h1>

                    <p className="text-gray-600 text-base">
                        The page you were looking for appears to have been moved, deleted, or does not exist.
                    </p>

                    <div className="flex justify-center">
                        <Button variant="warning" size="large" onClick={() => router.back()}>
                        <ChevronLeft />
                        <span className="ml-2 text-neutrals">Back to Home</span>
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ErrorPage;
