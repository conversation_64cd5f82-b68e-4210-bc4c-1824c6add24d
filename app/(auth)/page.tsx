import { Button } from '@/components/ui/buttons/button'
import Link from 'next/link'
import React from 'react'
import { Navbar } from './_components/navbar'
import { Footer } from './_components/footer'
import Image from 'next/image'

const IndexPage = () => {
  return (
    <div className="h-screen bg-[radial-gradient(circle_at_bottom_left,_var(--tw-gradient-stops))] from-primary/10 from-20% via-white via-40% to-primary">
        <Navbar/>
        <div className="relative isolate px-6 pt-40 lg:pt-14 lg:px-8 grid grid-cols-1 lg:grid-cols-2">
            <div className="mx-auto max-w-2xl py-32 sm:py-68 lg:py-56">
                <div className="hidden sm:mb-8 sm:flex sm:justify-center">
                    <div className="relative rounded-full px-3 py-1 text-sm leading-6 text-gray-600 ring-1 ring-neutral-900/10 hover:ring-neutral-900/20">
                        Visit our official website to learn more about us.{' '}
                        <a href="https://lifeguarddh.com" className="font-semibold text-primary">
                            <span className="absolute inset-0" aria-hidden="true" />
                            Visit now <span aria-hidden="true">→</span>
                        </a>
                        </div>
                </div>
                <div className="text-center">
                    <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
                        <div className="bg-gradient-to-r from-[#EBFDFF]/80 to-[#80D1D7]/50 px-4 p-2 text-transparent bg-clip-text">
                            Welcome Partners
                        </div>
                    </h1>
                    <p className="mt-6 text-lg leading-8 text-gray-600">
                        The realtime data reporting platform the helps your business grow by providing valuable insights into customer behaviour.
                    </p>
                    <div className="mt-10 flex items-center justify-center gap-x-6">
                        <Button className='rounded-full text-sm'>
                            <Link href={`${process.env.AUTH_URL}/signin`}>
                                Sign In
                            </Link>
                        </Button>
                        <Button className='rounded-full text-sm' variant={'outline'}>
                            <Link href={`${process.env.AUTH_URL}/signin`}>
                            Get Help <span aria-hidden="true"> → </span>
                            </Link>
                        </Button>
                    </div>  
                </div>
            </div>
            <div className='hidden xl:flex xl:justify-start xl:items-center xl:text-center'>
                <Image 
                    src="/landing.svg"
                    alt= "landing image"
                    height="400"
                    width="900"
                    className='aspect-4/3 rounded-lg animate-[bounce-1_3s_infinite]'
                />
            </div>
        </div>
        <Footer/>
    </div>
  )
}

export default IndexPage