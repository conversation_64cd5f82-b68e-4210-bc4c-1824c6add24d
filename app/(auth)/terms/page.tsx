import Terms from "./terms";
import Logo from "@/components/custom/logo/logo";
import CardHeader, {
  SubHeaderVariant,
} from "@/components/custom/header/header-card";

const TermsPage = () => {
  return (
    <>
      <div className="w-full h-screen bg-white text-black bg-[radial-gradient(circle_at_top_left,_var(--tw-gradient-stops))] from-primary/10 from-20% via-white via-55% to-primary overflow-y-auto">
        <div className="p-5 xl:mx-10.5 flex justify-left">
          <Logo />
        </div>
        <div className="flex flex-col gap-7.5 w-full h-auto pb-7">
          <div className="flex justify-center">
            <div className="rounded-2xl border border-stroke shadow-3 mx-3.5 my-1 xl:mx-15 xl:my-17 bg-white w-full">
              <div className="px-7 pt-6">
                <CardHeader
                  header="Terms and Condition"
                  subheader="Lorem Ipsum is simply dummy text of the printing and typesetting industry."
                  subheaderVariant={SubHeaderVariant.body5}
                  classNameHeaderVariant="text-primary"
                />
              </div>
              <div className="h-[calc(100vh-10rem-6rem-3rem)] overflow-y-auto pb-7">
                <Terms title="TERMS" data={data} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default TermsPage;

const data: any[] = [
  {
    id: 1,
    header: `Types of Data Collected`,
    text: `When you use our Services different types of Data is collected to provide and improve our Services.
    “Personal Data” is information used to identify you (such as your name, alias, phone number, and email address) which you provide when choosing to use one of our Services. When you choose to use our Services we rely on you to ensure the accuracy, completeness and currency of that information that you voluntarily provide.    
    “Usage data” is information about your use of our Services which may include information such as your computer’s Internet Protocol (IP) address, browser type, browser version, the pages of our Service that you visit, the time and date of your visit, the time spent on those pages, unique device identifiers and other diagnostic data. When you access the Services using a mobile device, this Usage Data may include information about the type of mobile device you use, your mobile device unique ID, the IP address of your mobile device, your mobile operating system, the type of mobile Internet browser you use, unique device identifiers and other diagnostic data.`,
  },
  {
    id: 2,
    header: `Cookies`,
    text: `Cookies are small files containing certain pieces of information that a website creates when you visit the site. It can track how and when you use a site, which site you visited immediately before, and it can store that information about you. Cookies cannot be used to run programs or deliver viruses to your computer. There are two types of cookies, session cookies and persistent cookies. We use session cookies which are stored in temporary memory and are not retained after you sign out or close the browser session.
    By using our Services, you consent to our use of cookies. You may choose to configure your browser to refuse all cookies or to indicate when a cookie is being sent, but please note that non-acceptance of cookies may impact functionality of our Services.`,
  },
  {
    id: 3,
    header: `Web Analytics`,
    text: `We do internal analytics and use Google Analytics, a third-party service to analyze Usage Data for the purpose of improving our Services. No IP address is collected as part of the internal analytics. Google Analytics creates unique user IDs for associating Usage Data for analytics purposes, thereby rendering the information anonymous.`,
  },
  {
    id: 4,
    header: `Use of Data`,
    text: `Data collected through our Service is used to:
       • provide and improve the Service to you
       • allow you to participate in interactive features of our Service when you choose to do so
       • respond to your requests for customer support
       • monitor usage of the Service
       • detect, prevent and address technical issues of the Service
       • meet regulatory, legal, audit, security and processing requirements
       • the extent permitted or required by applicable laws.
    We will not use your Personal Data for any other purpose without your prior consent`,
  },
  {
    id: 5,
    header: `Transfer, Processing and Retention of Data`,
    text: `While you voluntarily choose to use one of our Services, you agree that your Data may be transferred to multiple locations, whether within or outside Canada (unless there is a legal or regulatory requirement to store, access and/or use personal information in Canada). For example, our third-party service providers that process or store your data on our behalf may be located in the United States.
    Lifeguard Health Inc. will take all steps reasonably necessary to ensure that your Data is treated securely and in accordance with this Privacy Policy and that no transfer of your Data takes place to an organization, unless there are adequate controls in place including the security of your Data. Please note that the Data in the custody of these third-party platforms may be subject to access by the law enforcement authorities of those jurisdictions in which the third parties are located.    
    You may decide to discontinue use of our Service at any time, however Data that we have already collected from you will be retained and used by the Service only as long as necessary to fulfill the identified purposes or as permitted or required by law.`,
  },
  {
    id: 6,
    header: `Disclosure of Data`,
    text: `The Lifeguard App does not collect any user data without explicit consent. Data collected by the Lifeguard App, including the user’s 1) Name, 2) Phone Number, 3) Emergency Contact Name, 4) Emergency Contact Phone Number, 5) Location, is stored natively on the device hosting the app, and is not connected or distributed to any party including Lifeguard Digital Health, unless:
    • to provide emergency services to you when you are unable to respond to the timer alarm; in which the above data will be provided to Emergency Health Services and stored per their standard policy
    • to prevent and investigate possible wrongdoing in connection with the Service
    • to protect against legal liability as required to comply with any laws, regulations, court orders, subpoenas, or other legal process or investigation
    We will not share your Data for any other purpose without your prior consent.`,
  },
  {
    id: 7,
    header: `Security of Data`,
    text: `We take the security of your Data very seriously and have implemented reasonable administrative, technical and physical safeguards to keep your Data secure. 
    Despite these measures, we cannot guarantee its security as no method of transmission over the Internet or electronic storage is 100% secure.`,
  },
  {
    id: 8,
    header: `Links to Other Sites`,
    text: `Our Service may provide links to other websites that are not operated by us. We have no control over and assume no responsibility for the content, privacy policies or practices of any third-party websites or services. We encourage you to review the Privacy Policy of every site you visit and make your own decision regarding the information provided.`,
  },
  {
    id: 9,
    header: `Children’s Privacy`,
    text: `Our Services are not intended for anyone under the age of 18 (“Child” or ”Children”) unless with parental consent. If you are a parent or guardian and you are aware that your Child or Children have provided us with Personal Data, please contact us to arrange for its deletion from the Services.`,
  },
  {
    id: 10,
    header: `Changes to This Privacy Policy`,
    text: `This Privacy Policy may be updated from time to time. It is your responsible to review this Privacy Policy periodically for any changes that may impact your decision to continue use of our Services. Your continued use of the Services following posted changes to this Privacy Policy will signify your acceptance of any changes. Changes to this Privacy Policy are effective when they are posted on this page.`,
  },
  {
    id: 11,
    header: `Contact Us`,
    text: `If you have any questions or concerns about this Privacy Policy, please contact us by email: <EMAIL>`,
  },
];