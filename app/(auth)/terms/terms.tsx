import { MessageCircleQuestion } from "lucide-react";
import React from "react";
import Body from "../../../components/custom/typography/body";
import Label from "../../../components/custom/typography/label";

interface TermsPageProps {
  data: any[];
  title: string;
}

const Terms = ({ title, data }: TermsPageProps) => {
  return (
    <div>
      {data.map((item, index) => (
        <div key={index} className="flex gap-6 m-7.5">
          <div className="flex w-full flex-col">
            <Label variant={"label4"} font="bold">
              {item.header}
            </Label>
            <Body variant={"body6"}>{item.text}</Body>
            {/* <Body variant={"body6"} className="pt-4">
              {item.text.split("\n").map((line, lineIndex) => (
                <React.Fragment key={lineIndex}>
                  {line.startsWith("•") ? (
                    <div className="flex items-start">
                      <span className="mr-2"> •</span>
                      <span className="ml-4">{line.slice(2)}</span>
                    </div>
                  ) : (
                    <div>{line}</div>
                  )}
                  {lineIndex < item.text.split("\n").length - 1 && <br />}
                </React.Fragment>
              ))}
            </Body> */}
          </div>
        </div>
      ))}
    </div>
  );
};

export default Terms;
