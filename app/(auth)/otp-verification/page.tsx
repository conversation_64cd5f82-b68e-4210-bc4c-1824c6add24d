"use client";

import React from "react";
import OTPVerificationForm from "@/components/custom/auth/otp-verification/form-otp-verification";
import AuthLayout from "@/components/custom/auth/layout-auth";

const ResetPasswordPage = () => {
  return (
    <AuthLayout 
      header="Verify OTP" 
      subheader="Enter the 6 digit code sent to the registered email id along with the password that your want to set." 
      image="/auth/otp-verification.svg">
      {/* <OTPVerificationForm searchParams={"122346"} /> */}
      <div></div>
    </AuthLayout>
  );
};


export default ResetPasswordPage;
