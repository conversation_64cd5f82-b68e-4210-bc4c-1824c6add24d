import Logo from "@/components/ui/logo/logo"
import { Button } from "@/components/ui/buttons/button"
import Link from "next/link"

export const Navbar = () => {
    return (
      <header className="absolute inset-x-0 top-0 z-50">
        <nav className="flex items-center justify-between p-6 lg:px-8" aria-label="Global">
          <div className="flex lg:flex-1">
            <Logo />
          </div>
  
          <div className="hidden lg:flex lg:gap-x-12">
            <Link href="/" className="text-sm font-medium leading-6 text-gray-900">
              Home
            </Link>
            <a href="https://lifeguarddh.com/contact-us/" className="text-sm font-medium leading-6 text-gray-900" target="_blank" rel="noopener noreferrer">
              Contact
            </a>
            <a href="https://lifeguarddh.com/about-us/faqs/" className="text-sm font-medium leading-6 text-gray-900" target="_blank" rel="noopener noreferrer">
              About
            </a>
          </div>
  
          <div className="hidden lg:flex lg:flex-1 lg:justify-end">
            <Link href={`${process.env.AUTH_URL}/signin`}>
              <Button size="small" variant="primary" className="rounded-full w-[150px] text-sm">
                Sign In <span aria-hidden="true"> → </span>
              </Button>
            </Link>
          </div>
        </nav>
      </header>
    )
  }