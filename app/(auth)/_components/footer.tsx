import { Button } from "@/components/ui/buttons/button";
import Link from "next/link";
import LogoDark from "@/components/custom/logo/logo-dark";

export const Footer = () => {
  return (
    <div className="fixed bottom-0 w-full p-4 border border-stroke shadow-3 bg-white">
      <div className="md:max-w-screen-2xl mx-auto flex items-center w-full justify-between">
        <LogoDark />
        <div className="space-x-4 md:block md:w-auto flex items-center justify-between w-full">
          <Button size="small" variant="primary" className="text-sm">
            <Link href="https://lifeguarddh.com/privacy-policy/">
              Privacy Policy
            </Link>
          </Button>
          <Button size="small" variant="primary" className="text-sm">
            <Link href={`${process.env.AUTH_URL}/terms`}>Terms of Service</Link>
          </Button>
        </div>
      </div>
    </div>
  );
};
