import Card<PERSON>eader, {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/custom/header/header-card";
import SessionExpiredModel from "@/components/custom/model-session-expired";
import React, { useEffect, useState } from "react";
import DashboardCardsLayout from "@/components/custom/index/dashboard-cards-layout";
import {
  Users2,
  ListChecks,
  Download,
  Clock,
  Handshake,
  PackageOpen,
} from "lucide-react";
import ScreenVisitChart from "@/components/custom/charts/chart-screen-visit";
import { useMutation } from "@tanstack/react-query";
import { AddStat,GetStats } from "@/utils/api";
import { getCookie, USER_NAME } from "@/lib/cookie-store";

const AdminPage = () => {
  const [tokenExpired, setTokenExpired] = useState(false);
  const [topVisitedScreen, setTopVisitedScreen] = useState<any[]>([]);

  const [actionsCardData, setActionsCardData] = useState([
    { title: "Total Users", total: "0", icon: Users2 },
    { title: "Total Visits", total: "0", icon: ListChecks },
    { title: "Total Report Downloads", total: "0", icon: Download },
    { title: "Avg.Time", total: "0", icon: Clock },
    { title: "Total Clients", total: "0", icon: Handshake },
    { title: "Total Products", total: "0", icon: PackageOpen },
  ]);

  interface StatsResponse {
    total_users: string;
    total_visits: string;
    total_downloads: string;
    avg_time: string;
    total_clients: string;
    total_products: string;
    top_visited_screen: any; // Replace 'any' with a more specific type if possible
  }

  const mutation = useMutation({
    mutationFn: GetStats,
    onSuccess: async (data) => {
      setActionsCardData([
        { title: "Total Users", total: (data.data?.total_users ?? "0").toString(), icon: Users2 },
        { title: "Total Visits", total: (data.data?.total_visits ?? "0").toString(), icon: ListChecks },
        {
          title: "Total Report Downloads",
          total: (data.data?.total_downloads ?? "0").toString(),
          icon: Download,
        },
        { title: "Avg.Time", total: (data.data?.avg_time ?? "0").toString(), icon: Clock },
        { title: "Total Clients", total: (data.data?.total_clients ?? "0").toString(), icon: Handshake },
        {
          title: "Total Products",
          total: (data.data?.total_products ?? "0").toString(),
          icon: PackageOpen,
        },
      ]);
      setTopVisitedScreen(data.data?.top_visited_screen ?? []);
    }
  })

  const loadData = async () => {
    mutation.mutate()
  };

  useEffect(() => {
    loadData();
  }, []);

  return (
    <>
      <div className="w-full text-black">
        <div className="p-5 xl:mx-5"></div>
        <CardHeader
          header={`Welcome ${getCookie(USER_NAME)},`}
          subheader={``}
          headerVariant={HeaderVariant.header1}
        />
        <div className="col-span-12">
          <div className="grid grid-cols-2 gap-3 md:grid-cols-3 md:gap-4 xl:grid-cols-6">
            <DashboardCardsLayout data={actionsCardData} />
          </div>

          {topVisitedScreen && topVisitedScreen.length > 0 && (
            <div className="mt-6">
              <ScreenVisitChart data={topVisitedScreen} />
            </div>
          )}
        </div>
      </div>
      {/* <SessionExpiredModel
        setTokenExpired={ setTokenExpired }
        tokenExpired={ tokenExpired }
        isLoading = { mutation.isPending }
      /> */}
    </>
  );
};

export default AdminPage;
