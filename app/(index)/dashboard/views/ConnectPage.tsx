"use client"
import { useCookies } from 'next-client-cookies';
import Breadcrumb from "@/components/custom/breadcrumbs/Breadcrumb"
import DashboardCard from "@/components/custom/layouts/dashboard/dashboard-cards-layout"
import DashboardCardsNewLayout from '@/components/custom/layouts/dashboard/connect/dashboard-cards-new-layout';
import DashboardCsvCardsLayout from "@/components/custom/layouts/dashboard/connect/dashboard-csv-cards-layout"
import DashboardConnectCardsLayout from "@/components/custom/layouts/dashboard/connect/dashboard-connect-cards-layout"
import DashboardConnectCardsNewLayout from '@/components/custom/layouts/dashboard/connect/dashboard-connect-cards-new-layout';
import { AlarmCheck, Bell, Download, Eye, LucideIcon, Pause, Play, Plus, RotateCcw, Timer, User, User2, Users2 } from "lucide-react"
import DashboardFilterLayout from "@/components/custom/layouts/dashboard/dashboard-filter-layout"
import DashboardCardHeader from "@/components/custom/header/ header-dashboard-card"
import DropdownEllipsis from "@/components/custom/dropdown/dropdown-ellipsis"
import ScreenVisitLayout from "@/components/custom/layouts/dashboard/connect/layout-screen-visit"
import ScreenVisitChart from "@/components/custom/charts/connect/chart-screen-visit"
import { useContext, useEffect, useState } from "react"
import { FirebaseProvider, useFirebaseData } from  "@/contexts/fire-base-context"
import { DateProvider, DateContext } from '@/contexts/date-context'
import { CsvProvider } from "@/contexts/csv-context"
import { ACCESS_TOKEN, APP_CUSTOMER_ID, USER_PERMISSIONS, REFRESH_TOKEN, USER_ID, getCookie } from '@/lib/cookie-store'
import {EVENT_PAGE_VISIT} from '@/lib/events-store'
import {AddStat} from "@/utils/api";
import { checkCookie } from "@/lib/check-cookie"
import { useRouter } from "next/navigation";
import dynamic from 'next/dynamic';
// Dynamically import components with SSR disabled
const ChartDeviceAnalytics = dynamic(() => import('@/components/custom/charts/connect/chart-device-analytics'), {
  ssr: false,
});

const ChartSubstance = dynamic(() => import('@/components/custom/charts/connect/chart-substance'), {
  ssr: false,
});

const ChartResources = dynamic(() => import('@/components/custom/charts/connect/chart-resources'), { ssr: false });

const ChartCategories = dynamic(() => import('@/components/custom/charts/connect/chart-categories'), { ssr: false });

//const ChartScreenVisit = dynamic(() => import('@/components/custom/charts/connect/chart-screen-visit'), { ssr: false });

const userInitialCards = [
  {
    title: "Total Sessions",
    total: "100",
    icon: Eye
  },
  {
    title: "Total New Users",
    total: "100",
    icon: Users2
  },
  {
    title: "Total Active Users",
    total: "100",
    icon: User
  },
  {
    title: "Avg. Engagement Time",
    total: "100",
    icon: Timer
  },
]

const cards1= [
  {
    title: "Total Downloads",
    total: "100",
    icon: Download
  },
  {
    title: "Total Redownloads",
    total: "100",
    icon: Download
  },
]

const csvInitialCards = [
  {
    // Open new screen with overdose alert list
    title: "Total Overdose Alerts",
    total: "100",
    icon: Play
  },
  {
    title: "Total Lives Saved",
    total: "100",
    icon: Pause
  },
  {
    title: "Total Drug Notifications",
    total: "100",
    icon: Bell
  },
  {
    // Open new screen with overdose alert list
    title: "Confirmed Overdose Outcomes",
    total: "100",
    icon: AlarmCheck
  }
]

const csvKeyMapping = {
  'Total Overdose Alerts': 'Total_Overdose_Alerts',
  'Total Lives Saved': 'Total_Lives_Saved',
  'Total Drug Notifications': 'Total_Drug_Notifications',
  'Confirmed Overdose Outcomes': 'Confirmed_Overdose_Outcomes',
};


const userFirebaseKeyMapping = {
  "Total Sessions": { keys: ["session_start",'classic_session_start','métis_identified_session_start'], index: 0},
  "Total New Users": { keys: ["first_open",'classic_first_open','métis_identified_first_open'], index: 1},
  "Total Active Users": { keys:[ "screen_view"], index: 1 },
  "Avg. Engagement Time": { keys: ["user_engagement"], index: 3 },
};


const timerFirebaseKeyMapping = {
  "Total Start": { keys: ["click_timer_start", "classic_timer_start","classic_timer_start_"], index: 0 },
  "Total Pause": { keys: ["click_timer_pause",'classic_timer_paused','classic_timer_paused_','métis_identified_timer_paused','métis_identified_timer_start'], index: 0 },
  "Total Extend": { keys: ["click_timer_extend",'classic_timer_extend_300','classic_timer_extend_60','classic_timer_extend_180'], index: 0 },
  "Total Reset": { keys: ["click_timer_reset",'classic_timer_reset','classic_timer_reset_','métis_identified_timer_reset'], index: 0 },
  "Total Resume": { keys: ["click_timer_resume",'classic_timer_resume','classic_timer_resume_','métis_identified_timer_resume'], index: 0 },
};

const timerInitialCards = [
  {
    title: "Total Start",
    total: 100,
    icon: Play
  },
  {
    title: "Total Pause",
    total: 100,
    icon: Pause
  },
  {
    title: "Total Extend",
    total: 100,
    icon: Plus
  },
  {
    title: "Total Reset",
    total: 100,
    icon: RotateCcw
  },
  {
    title: "Total Resume",
    total: 100,
    icon: Play
  }
];

const csvUrl = '/data/event_counts_2025.csv';
 

const ConnectPage = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false)
  const [tokenExpired, setTokenExpired] = useState(false)
  const token = getCookie(ACCESS_TOKEN);
  const customer_id = getCookie(APP_CUSTOMER_ID);
  const user_id = getCookie(USER_ID);

  //Check if user data is already in cookies, otherwise fetch it from the API
  // useEffect(() => {
  //   checkCookie(cookies,token, user_id, setLoading);
  // }, [cookies,token, user_id]);
  
  
  useEffect(() => {
    const trackPageVisit = async () => {
      try {
        const result = {
          product: "connect",
          page: "dashboard",
        };

        // Call addStats for EVENT_PAGE_VISIT when the page loads
        await AddStat({
          event_name: EVENT_PAGE_VISIT,
          result: result,
          //customer_id: customer_id,
        });

        console.log("Stats recorded for page visit.");
      } catch (error) {
        console.error("Error tracking page visit:", error);
      }
    };

    trackPageVisit();
  }, [token, customer_id]);

return (
  <DateProvider>
    <FirebaseProvider>
      <CsvProvider csvUrl={csvUrl}>
        <div>

          <DashboardFilterLayout />

          <div className="mt-4 grid grid-cols-12 gap-4 md:mt-6 md:gap-6 2xl:mt-7.5 2xl:gap-7.5">
            <div className="col-span-12 ">
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-5 gap-6 ">
                <DashboardConnectCardsNewLayout 
                  initialCards={userInitialCards} 
                  firebaseKeyMapping={userFirebaseKeyMapping} 
                />
                <DashboardCardsNewLayout data={cards1} />
                <DashboardCsvCardsLayout 
                  initialCards={csvInitialCards} 
                  csvKeyMapping={csvKeyMapping} 
                />
              </div>
            </div>

            <div className="col-span-12">
              <div className="grid grid-cols-12 gap-6">
                <div className="col-span-6">
                  <ScreenVisitLayout />
                </div>
                <div className="col-span-6">
                  <ScreenVisitChart />
                </div>

                <div className="col-span-6">
                  <ChartDeviceAnalytics />
                </div>
                <div className="col-span-6">
                  <ChartSubstance />
                </div>
              </div>
            </div>


            <div className="col-span-12">
              <div className="rounded-2xl bg-white border-0 border-neutralsmildGrey border-stroke shadow-md shadow-secondary/25">
                <div className="relative z-0 w-full p-7.5">
                  <DashboardCardHeader title="Timer Report" description='Short explanation for the client.' />
                  <div className="absolute inset-0 flex justify-end items-center z-10 pr-6">
                    <DropdownEllipsis page="/dashboard/lite/temperature" />
                  </div>
                </div>
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-5 pb-7.5 px-7.5">
                  <DashboardConnectCardsLayout 
                    initialCards={timerInitialCards} 
                    firebaseKeyMapping={timerFirebaseKeyMapping} 
                  />
                </div>
              </div>
            </div>

            <ChartResources />
            <ChartCategories />

          </div>

        </div>
      </CsvProvider>
    </FirebaseProvider>
  </DateProvider>
);
}
export default ConnectPage;
