'use client';
import { notFound, useRouter, usePathname, useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react'
import AdminPage from './views/AdminPage';
import ConnectPage from './views/ConnectPage';
import LitePage from './views/LitePage';
import { getArrayCookie, getCookie, LAST_ACTIVE_DASHBOARD, USER_PERMISSIONS } from '@/lib/cookie-store';
import Breadcrumb from '@/components/custom/breadcrumbs/Breadcrumb';
import { useSecureRouter } from '@/lib/navigation';
import BuildStrongPage from './views/BuildStrongPage';

const DashboardPage = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { securePush } = useSecureRouter();

  const [resolvedPermission, setResolvedPermission] = useState<string | null>(null);

  useEffect(() => {
    const permission = searchParams.get('permission');
    if (permission) {
      setResolvedPermission(permission);
    } else {
      const fallback = getCookie(LAST_ACTIVE_DASHBOARD) ?? '';
      securePush('/dashboard', { permission: fallback });
    }
  }, [searchParams]);

  const permissions = getArrayCookie(USER_PERMISSIONS);

  if (!resolvedPermission) {
    return null;
  }

  return (
    <div>
      <h1>Dashboard</h1>
      <div className="flex gap-2 flex-wrap">
        {permissions.map((element, index) => (
          <Breadcrumb pageName={element} onClick={() => securePush('/dashboard', { permission: element })} key={index} active={resolvedPermission} />
        ))}
      </div>

      <div>
        {resolvedPermission === 'admin' && <AdminPage />}
        {resolvedPermission === 'connect' && <ConnectPage />}
        {resolvedPermission === 'lite' && <LitePage />}
        {resolvedPermission === 'buildstrong' && <BuildStrongPage />}
      </div>
    </div>
  );
};

export default DashboardPage;