import WarningModal from "@/components/custom/modal/modal-warning";
import Spinner from "@/components/ui/spinner";
import { ACCESS_TOKEN, getCookie } from "@/lib/cookie-store";
import { Logout } from "@/utils/api";
import { useMutation } from "@tanstack/react-query";
import { signOut } from "next-auth/react";
import { useRouter } from "next/navigation";
import React, { useEffect, useRef, useState } from "react";

interface WarningModelProps {
  modalOpen: Boolean;
  setModalOpen: (arg: boolean) => void;
}

const LogoutLayout = ({
  modalOpen = false,
  setModalOpen,
}: WarningModelProps) => {
  const router = useRouter();
  const trigger = useRef<any>(null);
  const modal = useRef<any>(null);

  const token = getCookie(ACCESS_TOKEN);

  // close on click outside
  useEffect(() => {
    const clickHandler = ({ target }: MouseEvent) => {
      if (!modal.current) return;
      if (
        !modalOpen ||
        modal.current.contains(target) ||
        trigger.current.contains(target)
      )
        return;
      setModalOpen(false);
    };
    document.addEventListener("click", clickHandler);
    return () => document.removeEventListener("click", clickHandler);
  });

  // close if the esc key is pressed
  useEffect(() => {
    const keyHandler = (event: KeyboardEvent) => {
      if (!modalOpen || event.key !== "Escape") return;
      setModalOpen(false);
    };
    document.addEventListener("keydown", keyHandler);
    return () => document.removeEventListener("keydown", keyHandler);
  });

  interface LogoutResponse {
    status: number;
    message?: string;
  }

  const mutation = useMutation({
    mutationFn: Logout,
    onSuccess: async (data) => {
      if (data.code == 0) {
        throw new Error("Something went wrong");
      }
      if(data.code == 1){
        try {
          await signOut({ redirect: true });
        } catch (error) {}
    
        router.push(`${process.env.AUTH_URL}/`);
      }
    }
  })

  const onConfirm = async () => {
    mutation.mutate()
  };

  return (
    <>
      <WarningModal
        modalOpen={modalOpen}
        setModalOpen={setModalOpen}
        setConfirm={onConfirm}
        setDismiss={() => setModalOpen(false)}
        title={"Are you sure you want to Log Out?"}
        description={
          "Logging out will end your session. Remember, you'll need to log in again to access the app and your information."
        }
        buttonTitle={"Logout"}
      />
       <Spinner show={mutation.isPending} />
    </>
  );
};

export default LogoutLayout;
