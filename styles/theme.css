@font-face {
    font-family: 'Metropolis';
    src: url('/fonts/Metropolis-Regular.otf') format('otf');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Metropolis';
    src: url('/fonts/Metropolis-Medium.otf') format('otf');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Metropolis';
    src: url('/fonts/Metropolis-SemiBold.otf') format('otf');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Metropolis';
    src: url('/fonts/Metropolis-Bold.otf') format('otf');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Metropolis';
    src: url('/fonts/Metropolis-Light.otf') format('otf');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Metropolis';
    src: url('/fonts/Metropolis-ExtraBold.otf') format('otf');
    font-weight: 800;
    font-style: normal;
    font-display: swap;
}

@theme {

    --font-metropolis: Metropolis, sans-serif;

    --breakpoint-3xl: 118.75rem;

    /* Theme colours */
    --color-primary: #00A2B0;
    --color-warning: #B91C1C;
    --color-primary: #00A2B0;
    --color-primarybutton: #00A2B0;
    --color-primaryhover: #80D1D7;

    --color-secondary: #172B3E;
    --color-secondarybutton: #0C4B5E;
    --color-secondaryhover: #85A5AE;

    --color-success: #A7F3D0;

    --color-neutrals: #FDFDFD;
    --color-neutralsdark: #201A1B;
    --color-neutralsrichGrey: #737373;
    --color-neutralsmildGrey: #D9D9D9;

    --color-lightTeal: #55AA81;
    --color-lightTealHover: #AAD5C0;
    --color-lightTealBg: #E3FFF0;

    --color-mutedTeal: #4A8C91;
    --color-mutedTealHover: #A4C5C8;
    --color-mutedTealBg: #DCFCF9;

    --color-blueTeal: #00A2B0;
    --color-blueTealHover: #80D1D7;
    --color-blueTealBg: #EBFDFF;

    --color-oceanBlue: #2D75AE;
    --color-oceanBlueHover: #96BAD7;
    --color-oceanBlueBg: #F0F9FF;

    --color-blueViolet: #374788;
    --color-blueVioletHover: #9BA3C3;
    --color-blueVioletBg: #E6EDFF;

    --color-midnightBlue: #172B3E;
    --color-midnightBlueHover: #8B959E;
    --color-midnightBlueBg: #EAF5FF;

    --color-deepTeal: #0C4B5E;
    --color-deepTealHover: #85A5AE;
    --color-deepTealBg: #E2F8FF;
    
    /* Font Sizes */
    --text-base: 1rem;
    --text-lg: 1.15rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-sm: 0.875rem;
    --text-xs: 0.75rem;

    /* Line Heights */
    --text-4xl--line-height: 2.5rem;
    --text-3xl--line-height: 2.25rem;
    --text-2xl--line-height: 2rem;
    --text-xl--line-height: 1.75rem;
    --text-lg--line-height: 1.625rem;
    --text-base--line-height: 1.5rem;
    --text-sm--line-height: 1.25rem;
    --text-xs--line-height: 1rem;

    /* Font Weights */
    --font-weight-light: 300;
    --font-weight-regular: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 700;

    --drop-shadow-xs: 0 1px 1px rgb(0 0 0 / 0.05);
    --drop-shadow-sm: 0 1px 2px rgb(0 0 0 / 0.15);
    --drop-shadow-md: 0 3px 3px rgb(0 0 0 / 0.12);
    --drop-shadow-lg: 0 4px 4px rgb(0 0 0 / 0.15);
    --drop-shadow-xl: 0 9px 7px rgb(0 0 0 / 0.1);
    --drop-shadow-2xl: 0 25px 25px rgb(0 0 0 / 0.15);
    --drop-shadow-dropShadow: 2px 4px 16px rgba(0, 0, 0, 0.08);
}

@layer components {
    .typography {
        p {
            font-size: var(--text-base);
            color: var(--color-gray-700);
        }

        h1 {
            font-size: var(--text-4xl) !important;
            line-height: var(--text-4xl--line-height)!important;
            font-weight: var(--font-weight-bold)!important;
            color: var(--color-neutralsdark);
        }

        h2 {
            font-size: var(--text-3xl)!important;
            line-height: var(--text-3xl--line-height)!important;
            font-weight: var(--font-weight-bold);
            color: var(--color-neutralsdark);
        }

        h3 {
            font-size: var(--text-2xl)!important;
            line-height: var(--text-2xl--line-height)!important;
            font-weight: var(--font-weight-bold);
            color: var(--color-neutralsdark);
        }

        h4 {
            font-size: var(--text-xl)!important;
            line-height: var(--text-xl--line-height)!important;
            font-weight: var(--font-weight-bold);
            color: var(--color-neutralsdark);
        }

        h5 {
            font-size: var(--text-lg)!important;
            line-height: var(--text-lg--line-height)!important;
            font-weight: var(--font-weight-semibold);
            color: var(--color-neutralsdark);
        }

        h6 {
            font-size: var(--text-base)!important;
            line-height: var(--text-base--line-height)!important;
            font-weight: var(--font-weight-semibold);
            color: var(--color-neutralsdark);
        }
        

        .body1 {
            font-size: var(--text-2xl) !important;
            line-height: var(--text-2xl--line-height) !important;
            font-weight: var(--font-weight-normal) !important;
            color: var(--color-neutralsdark);
        }
        
        .body2 {
            font-size: var(--text-xl) !important;
            line-height: var(--text-xl--line-height) !important;
            font-weight: var(--font-weight-normal);
            color: var(--color-neutralsdark);
        }
        
        .body3 {    
            font-size: var(--text-lg) !important;
            line-height: var(--text-lg--line-height) !important;
            font-weight: var(--font-weight-normal   );
            color: var(--color-neutralsdark);
        }
        
        .body4 {
            font-size: var(--text-base) !important;
            line-height: var(--text-base--line-height) !important;
            font-weight: var(--font-weight-normal);
            color: var(--color-neutralsdark);
        }
        
        .body5 {
            font-size: var(--text-sm) !important;
            line-height: var(--text-sm--line-height) !important;
            font-weight: var(--font-weight-normal);
            color: var(--color-neutralsdark);
        }
        
        .body6 {
            font-size: var(--xs-base) !important;
            line-height: var(--text-xs--line-height) !important;
            font-weight: var(--font-weight-normal) !important;
            color: var(--color-neutralsdark);
        }
    } 
}