import Link from 'next/link'
import React from 'react'

interface FormLinkProps{
    question: string
    answer: string
    link: string
    onClick?: () => void
    className?: string
}

const FormLink = ({question, answer, link, onClick, className}: FormLinkProps) => {
  return (
    <div className={`text-center text-black/90 ${className}`}>
        <p>
        {question}?{" "}
        {onClick ? (
          <a className="text-primary" onClick={onClick}>
            {answer}
          </a>
        ) : (
          <Link href={`${process.env.AUTH_URL}${link}`} className="text-primary">
              {answer}
          </Link>
        )}
        
        </p>
    </div>
  )
}

export default FormLink