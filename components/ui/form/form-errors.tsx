import Label from "@/components/custom/typography/label"
import { XCircle  } from "lucide-react"

interface FormErrorsProps {
    id: string
    errors?: String
}

export const FormErrors = ({
    id,
    errors
}: FormErrorsProps) => {
    if(!errors){ return null }
    return(
        <div className="flex text-left items-center p-2 text-red-700 rounded-lg w-fit">
            <XCircle className="h-4 w-4 mr-2"/> 
            <Label variant={"label6"} className="items-center">
                {errors}
            </Label>
        </div>
    )
}

