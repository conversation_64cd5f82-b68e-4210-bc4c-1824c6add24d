"use client";

import { Change<PERSON><PERSON><PERSON><PERSON><PERSON>, forwardRef } from "react";
import { useFormStatus } from "react-dom";
import { FormErrors } from "./form-errors";
import { FieldError } from "react-hook-form"

interface FormInputProps extends React.InputHTMLAttributes<HTMLInputElement>{
  id: string
  label?: string
  type?: string
  className?: string
  placeholder?: string
  disabled?: boolean
  error?: FieldError
  onNext?: () => void
  onEscape?: () => void
}

export const FormInput = forwardRef<HTMLInputElement, FormInputProps>(
  (
    {
      id,
      label,
      type,
      placeholder,
      disabled,
      error,
      className,
      onNext,
      onEscape,
      ...props
    },
    ref
  ) => {
    const { pending } = useFormStatus();
    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if(e.key === 'Enter') {
        onNext?.()
      } else if (e.key === 'Return') {
        onEscape?.()
      }
    }
    return (
      <div className="space-y-2">
        <div className="space-y-1">
          {label ? (
            <label
              className="mb-2.5 block font-medium text-neutral-600"
              htmlFor={id}
            >
              {label}
            </label>
          ) : null}
          <input
            name={id}
            id={id}
            placeholder={placeholder}
            type={type}
            ref={ref}
            disabled={pending || disabled}
            className={`w-full rounded-lg border ${error ? 'border-red-700' : 'border-stroke'} bg-transparent py-4 pl-6 pr-10 text-black outline-none focus:border-primary focus-visible:shadow-none ${className}`}
            aria-describedby={`${id}-error`}
            onKeyDown={handleKeyDown}
            {...props}
          />
        </div>
        {error && <FormErrors id={id as string} errors={error.message} />}
      </div>
    );
  }
);

FormInput.displayName = "FormInput";
