"use client"

import { KeyboardEventHandler, forwardRef } from "react"
import { cn } from "@/lib/utils"
import { FormErrors } from "./form-errors"
import { useFormStatus } from "react-dom"
import { FieldError } from "react-hook-form"

interface FormTextareaProps extends React.InputHTMLAttributes<HTMLTextAreaElement>{
    id: string
    label?: string
    type?: string
    className?: string
    placeholder?: string
    disabled?: boolean
    error?: FieldError
    rows: number
    onNext?: () => void
    onEscape?: () => void
}

export const FormTextarea = forwardRef<HTMLTextAreaElement, FormTextareaProps>(
(
    {
        id,
        label,
        type,
        placeholder,
        disabled,
        error,
        className,
        rows = 1,
        onNext,
        onEscape,
        ...props
    },
    ref
) => {

    const { pending } = useFormStatus()
    const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
        if(e.key === 'Enter') {
          onNext?.()
        } else if (e.key === 'Return') {
          onEscape?.()
        }
    }

    return(
       <div className="space-y-2 w-full">
            <div className="space-y-1 w-full">
                {label ? (
                    <label
                        htmlFor={id}
                        className="font-semibold text-neutral-700"
                    >
                        {label}
                    </label>
                ): null}
                <textarea
                    ref={ref}
                    placeholder={placeholder}
                    name={id}
                    id={id}
                    disabled={pending || disabled}
                    className={`w-full rounded border ${error ? 'border-red-700' : 'border-stroke'} bg-gray py-3 pl-11.5 pr-4.5 text-black focus:border-primary focus-visible:outline-none dark:border-strokedark dark:bg-meta-4 dark:text-white dark:focus:border-primary ${className}`}
                    aria-describedby={`${id}-error`}
                    rows={rows}
                    onKeyDown={handleKeyDown}
                    {...props}
                />
                {error && <FormErrors id={id as string} errors={error.message} />}
            </div>
       </div>
    )
})

FormTextarea.displayName = "FormTextarea"
