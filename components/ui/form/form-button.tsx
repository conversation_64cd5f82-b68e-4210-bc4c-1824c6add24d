import React from 'react'

interface FormButtonProps{
    type: string
    answer: string
    link: string
    className?: string
}

const FormButton = () => {
  return (
    <div className="mb-5">
        <input
        type="submit"
        value="Sign In"
        className="w-full cursor-pointer rounded-lg border border-primary bg-primary p-4 text-white transition hover:bg-opacity-90"
        />
    </div>
  )
}

export default FormButton