import { FormInput } from '@/components/ui/form/form-input';
import { CheckSquare2, ChevronDown, SquareIcon } from 'lucide-react';
import React, { forwardRef, useState } from 'react'
import { FieldError } from "react-hook-form"

interface FormInputMultiDropdownProps extends React.InputHTMLAttributes<HTMLSelectElement>{
    id: string
    label?: string
    className?: string
    disabled?: boolean
    error?: FieldError
    selected: string[]
    data: any[]
    onNext?: () => void
    onEscape?: () => void
    setSelected?: () => string[]
    children?: React.ReactNode
}

export const FormInputMultiDropdown = forwardRef<HTMLSelectElement, FormInputMultiDropdownProps>(
(
    {
        id,
        label,
        disabled,
        error,
        className,
        data,
        selected,
        setSelected
    },
    ref
) => {
    const [show, setShow] = useState(false)

    const onItemClick = (value: any) => {
        console.log(value)
        if(selected.includes(value.name)) {
            selected = selected.filter(item => item!== value.name)
        } else {
            selected = [...selected, value.name]
        }
    }

    return (
        <div>
            <div className="relative mb-1">
                <FormInput
                    id={id}
                    label={label}
                    placeholder='select mulitple fruits'
                    value={selected}
                    error={error}
                    onClick={() =>setShow(!show)}
                    className='bg-white'
                />
                <span className="absolute right-4 top-11 text-black/50">
                    <ChevronDown className="h-5 w-5" />
                </span>
            </div>
            
            {
                show &&
                <div className='bg-white rounded-lg'>
                    {   data &&
                        data.map((item, index) => {
                            return (
                                <div 
                                    className='flex border-b px-4.5 py-2.5 gap-4'
                                    onClick={() => onItemClick(item)}
                                    // onClick={() => setSelected(item)}
                                    key={item.id}
                                >
                                    {
                                        selected.find((val) => val === item.name) ?
                                        <CheckSquare2 className='h-5 w-5' /> :
                                        <SquareIcon className='h-5 w-5' />
                                    }
                                    {item.name}
                                </div>
                            )
                        })
                    }
                </div>
            }
        </div>
    );
})
