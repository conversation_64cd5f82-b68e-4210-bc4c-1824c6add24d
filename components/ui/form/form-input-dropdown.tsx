import { forwardRef } from "react"
import { FormErrors } from "./form-errors"
import { useFormStatus } from "react-dom"
import { FieldError } from "react-hook-form"

interface FormTextareaProps extends React.InputHTMLAttributes<HTMLSelectElement>{
    id: string
    label?: string
    className?: string
    disabled?: boolean
    error?: FieldError
    onNext?: () => void
    onEscape?: () => void,
    children?: React.ReactNode
}

export const FormInputDropdown = forwardRef<HTMLSelectElement, FormTextareaProps>(
(
    {
        id,
        label,
        disabled,
        error,
        className,
        onNext,
        onEscape,
        children,
        ...props
    },
    ref
) => {

    const { pending } = useFormStatus()
    const handleKeyDown = (e: React.KeyboardEvent<HTMLSelectElement>) => {
        if(e.key === 'Enter') {
          onNext?.()
        } else if (e.key === 'Return') {
          onEscape?.()
        }
    }

    return(
       <div className="space-y-2 w-full">
            <div className="space-y-1 w-full">
                {label ? (
                    <label
                        htmlFor={id}
                        className="font-semibold text-neutral-700"
                    >
                        {label}
                    </label>
                ): null}
                <select 
                    id={id}
                    ref={ref}
                    disabled={pending || disabled}
                    className={`w-full rounded-lg border ${error ? 'border-red-700' : 'border-stroke'} appearance-none bg-gray py-3 px-4.5 text-black focus:border-primary focus-visible:outline-none${className}`}
                    aria-describedby={`${id}-error`}
                    onKeyDown={handleKeyDown}
                    {...props}
                >
                    {children}
                </select>
                {error && <FormErrors id={id as string} errors={error.message} />}
            </div>
       </div>
    )
})

