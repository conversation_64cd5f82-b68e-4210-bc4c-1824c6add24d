import { AlertTriangle } from 'lucide-react';
import React from 'react'
import CardHeader, { HeaderVariant, SubHeaderVariant } from '../header/header-card';

interface WarningModelProps{
    modalOpen: Boolean
    setModalOpen: (arg: boolean) => void
    setConfirm: (arg: boolean) => void
    setDismiss: (arg: boolean) => void
    title: string
    description: string
    buttonTitle: string
}

const WarningModal = ({ modalOpen, setModalOpen, title, description, buttonTitle, setConfirm, setDismiss }: WarningModelProps) => {
    return (
      <div>
        <div
          className={`fixed left-0 top-0 z-999999 flex h-full min-h-screen w-full items-center justify-center bg-black/90 px-4 py-5 ${
            modalOpen ? "block" : "hidden"
          }`}
        >
          <div
            onFocus={() => setModalOpen(true)}
            onBlur={() => setModalOpen(false)}
            className="w-full max-w-150 rounded-lg bg-white px-8 py-12 text-center dark:bg-boxdark md:px-17.5 md:py-15"
          >
            <span className="mx-auto inline-block bg-red-50 p-3 rounded-full">
                <AlertTriangle className='h-8 w-8 text-red-700'/>
            </span>
            <CardHeader 
                header = {title}              
                subheader = {description}
                headerVariant={HeaderVariant.header3}
                subheaderVariant={SubHeaderVariant.body5}
            />
            <div className="-mx-3 flex flex-wrap gap-y-4">
              <div className="w-full px-3 2xsm:w-1/2">
                <button
                  onClick={() => setDismiss(true)}
                  className="block w-full rounded-md border border-stroke bg-gray p-3 text-center font-medium text-black transition hover:border-red-700 hover:bg-red-700 hover:text-white dark:border-strokedark dark:bg-meta-4 dark:text-white dark:hover:border-red-700 dark:hover:bg-red-700"
                >
                  Cancel
                </button>
              </div>
              <div className="w-full px-3 2xsm:w-1/2">
                <button 
                  onClick={() => setConfirm(true)}
                  className="block w-full rounded-md border bg-red-700 p-3 text-center font-medium text-white transition hover:bg-opacity-90"
                >
                    {buttonTitle}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
}

export default WarningModal