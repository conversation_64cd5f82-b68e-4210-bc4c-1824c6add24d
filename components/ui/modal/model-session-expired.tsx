"use client";
import React, { useEffect } from "react";
import WarningModal from "./modal-warning";
import {
  forceLogout,
  renewSession,
  tokenValidationCheck,
} from "@/services/api";
import { useRouter } from "next/navigation";
import { useMutation } from "@tanstack/react-query";
import Spinner from "../spinner";

interface SessionExpiredModelProps {
  tokenExpired: boolean;
  setTokenExpired: (arg: boolean) => void;
  loading?: boolean
}

const SessionExpiredModel = ({
  tokenExpired,
  setTokenExpired,
  loading = false
}: SessionExpiredModelProps) => {
  var router = useRouter();
  const validationMutation = useMutation({
    mutationFn: tokenValidationCheck,
    onSuccess: async (data) => {
      // if (data.status == 0) {
      //   setTokenExpired(true);
      //   return;
      // }
    }
  })

  const sessionRenewMutation = useMutation({
    mutationFn: renewSession,
    onSuccess: async (data) => {
      // if (data.status == 0) {
      //   setTokenExpired(true);
      //   return;
      // }
      setTokenExpired(false);
      router.refresh();
    }
  })

  const forceLogoutMutation = useMutation({
    mutationFn: forceLogout,
    onSuccess: async (data) => {
      // if (data.status == 0) {
      //   setTokenExpired(true);
      //   return;
      // }
      // setTokenExpired(false);
      // try { await signOut({ redirect: true }); } 
      // catch (error) {}
  
      router.refresh();
      router.push(`${process.env.AUTH_URL}/`);
    }
  })


  /**
   * @param(tokenExpired) is not false, we call the api to check the token validity
   * If the token is expired, we show the error message and refresh the session button
   **/

  const onLoad = async () => {
    if (tokenExpired == true) return;
    // var request = { refresh_token: user?.refresh_token };
    // validationMutation.mutate(request)
  };

  const onConfirm = async () => {
    // var request = { refresh_token: user?.refresh_token };
    // sessionRenewMutation.mutate(request)
  };

  const onDismiss = async () => {
    // var request = { refresh_token: user?.refresh_token };
    // forceLogoutMutation.mutate(request)
  };

  useEffect(() => {
    onLoad();
  }, []);

  return (
    <>
      {tokenExpired && (
        <WarningModal
          setModalOpen={setTokenExpired}
          modalOpen={tokenExpired}
          title={`Session expired!`}
          description={`Do you want to continue using the session?`}
          buttonTitle="Refresh"
          setConfirm={() => onConfirm()}
          setDismiss={() => onDismiss()}
        />
      )}
      <Spinner show={ validationMutation.isPending || sessionRenewMutation.isPending || forceLogoutMutation.isPending || loading } />
    </>
  );
};

export default SessionExpiredModel;
