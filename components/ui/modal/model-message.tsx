import React, { useState, useEffect, useRef } from "react";

interface MessageModelProps{
  modalOpen: Boolean
  setModalOpen: (arg: boolean) => void
  setConfirm: (arg: boolean) => void
  title: string
  description: string
  buttonTitle: string
}

const MessageModal= ({ modalOpen, setModalOpen, title, description, buttonTitle, setConfirm}: MessageModelProps) => {
  return (
    <div>
      <div
        className={`fixed left-0 top-0 z-999999 flex h-full min-h-screen w-full items-center justify-center bg-black/90 px-4 py-5 ${
          modalOpen ? "block" : "hidden"
        }`}
      >
        <div
          onFocus={() => setModalOpen(true)}
          onBlur={() => setModalOpen(false)}
          className="w-full max-w-142.5 rounded-lg bg-white px-8 py-12 text-center dark:bg-boxdark md:px-17.5 md:py-15"
        >
          <h3 className="pb-2 text-xl font-bold text-black dark:text-white sm:text-2xl">
            {title}
          </h3>
          <span className="mx-auto mb-6 inline-block h-1 w-22.5 rounded bg-primary"></span>
          <p className="mb-10"> {description} </p>
          <div className="-mx-3 flex flex-wrap gap-y-4">
            <div className="w-full px-3 2xsm:w-1/2">
              <button
                onClick={() => setModalOpen(false)}
                className="block w-full rounded-lg border border-stroke bg-gray p-3 text-center font-medium text-black transition hover:border-red-700 hover:bg-red-700 hover:text-white"
              >
                Cancel
              </button>
            </div>
            <div className="w-full px-3 2xsm:w-1/2">
              <button 
                className="block w-full rounded-lg border border-primary bg-primary p-3 text-center font-medium text-white transition hover:bg-opacity-90"
                onClick={() => setConfirm(true)}
              >
                {buttonTitle}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MessageModal;
