import { useRouter } from 'next/navigation';
import React, { useEffect, useRef, useState } from 'react'
import MessageModal from './modal/model-message';

interface SuccessModelProps{
    modalOpen: Boolean
    title: string
    description: string
    buttonTitle: string
    setModalOpen: (arg: boolean) => void
    onConfirm?: (arg: boolean) => void
}

const SuccessModel = ({ modalOpen = false, setModalOpen, title, description, buttonTitle, onConfirm}: SuccessModelProps) => {
    const router = useRouter()
    const trigger = useRef<any>(null);
    const modal = useRef<any>(null);

    // close on click outside
    useEffect(() => {
      const clickHandler = ({ target }: MouseEvent) => {
        if (!modal.current) return;
        if (!modalOpen || modal.current.contains(target) || trigger.current.contains(target))
          return;
        setModalOpen(false);
      };
      document.addEventListener("click", clickHandler);
      return () => document.removeEventListener("click", clickHandler);
    });
  
    // close if the esc key is pressed
    useEffect(() => {
      const keyHandler = (event: KeyboardEvent) => {
        if (!modalOpen || event.key !== "Escape") return;
        setModalOpen(false);
      };
      document.addEventListener("keydown", keyHandler);
      return () => document.removeEventListener("keydown", keyHandler);
    });

    const confirm = async () => {
      setModalOpen(!modalOpen)
      onConfirm
      router.refresh()
    }

    return (
        <MessageModal 
            modalOpen={modalOpen}
            setModalOpen={setModalOpen} 
            setConfirm={() => confirm()}
            title={title} 
            description={description} 
            buttonTitle={buttonTitle}           
        />
    )
}

export default SuccessModel