import React from "react";

interface SpinnerProps{
    show: boolean
}

const Spinner = ({ show }: SpinnerProps) => {
  return (
    <div>
      <div
        className={`fixed left-0 top-0 z-999999 flex h-full min-h-screen w-full items-center justify-center bg-black/90 px-4 py-5 ${
          show ? "block" : "hidden"
        }`}
      >

        <div className="flex items-center flex-col gap-4">
            <div className="animate-spin">
                <svg
                    width="50"
                    height="50"
                    viewBox="0 0 50 50"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <mask id="path-1-inside-1_1881_16176" fill="white">
                        <path d="M31.9333 49.0195C37.093 47.5301 41.636 44.4188 44.8896 40.1462C48.1432 35.8736 49.9345 30.6668 49.9982 25.2968C50.062 19.9268 48.3948 14.679 45.2435 10.3304C42.0922 5.98178 37.6244 2.76352 32.5014 1.15209C27.3785 -0.459331 21.8728 -0.378254 16.7996 1.38332C11.7263 3.1449 7.35519 6.49332 4.33332 10.9328C1.31145 15.3724 -0.200496 20.667 0.0213371 26.0328C0.24317 31.3986 2.18699 36.5503 5.565 40.7253L9.8072 37.2929C7.16653 34.0292 5.647 30.002 5.47359 25.8074C5.30018 21.6128 6.4821 17.4739 8.84437 14.0034C11.2066 10.5329 14.6237 7.91538 18.5895 6.53831C22.5554 5.16125 26.8593 5.09787 30.8641 6.35756C34.8688 7.61724 38.3614 10.133 40.8248 13.5325C43.2883 16.9319 44.5915 21.0342 44.5417 25.2321C44.4919 29.4299 43.0916 33.5002 40.5482 36.8402C38.0048 40.1801 34.4534 42.6123 30.4199 43.7766L31.9333 49.0195Z" />
                    </mask>
                    <path
                        d="M31.9333 49.0195C37.093 47.5301 41.636 44.4188 44.8896 40.1462C48.1432 35.8736 49.9345 30.6668 49.9982 25.2968C50.062 19.9268 48.3948 14.679 45.2435 10.3304C42.0922 5.98178 37.6244 2.76352 32.5014 1.15209C27.3785 -0.459331 21.8728 -0.378254 16.7996 1.38332C11.7263 3.1449 7.35519 6.49332 4.33332 10.9328C1.31145 15.3724 -0.200496 20.667 0.0213371 26.0328C0.24317 31.3986 2.18699 36.5503 5.565 40.7253L9.8072 37.2929C7.16653 34.0292 5.647 30.002 5.47359 25.8074C5.30018 21.6128 6.4821 17.4739 8.84437 14.0034C11.2066 10.5329 14.6237 7.91538 18.5895 6.53831C22.5554 5.16125 26.8593 5.09787 30.8641 6.35756C34.8688 7.61724 38.3614 10.133 40.8248 13.5325C43.2883 16.9319 44.5915 21.0342 44.5417 25.2321C44.4919 29.4299 43.0916 33.5002 40.5482 36.8402C38.0048 40.1801 34.4534 42.6123 30.4199 43.7766L31.9333 49.0195Z"
                        stroke="#00A7B5"
                        strokeWidth="14"
                        mask="url(#path-1-inside-1_1881_16176)"
                    />
                </svg>
            </div>
            <div className="text-white">
                <div className="body6"> Loading....</div>
            </div>
        </div>
    </div>
    </div>
  );
};

export default Spinner;
