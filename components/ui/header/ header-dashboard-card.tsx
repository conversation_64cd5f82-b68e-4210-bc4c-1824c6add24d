import React from 'react'
import CardHead<PERSON>, { HeaderVariant, SubHeaderVariant } from './header-card'

interface DashboardCardHeaderProps{
    title: string
    description?: string
}

const DashboardCardHeader = ({ title, description}: DashboardCardHeaderProps) => {
  return (
    <div className="flex items-start justify-between border-b border-stroke px-6 py-5 pt-6">
        <CardHeader 
            header={title}
            subheader={description}
            headerVariant={ HeaderVariant.header6 }
            subheaderVariant={ SubHeaderVariant.body6 }
            className='mb-0'
        />
    </div>
  )
}

export default DashboardCardHeader