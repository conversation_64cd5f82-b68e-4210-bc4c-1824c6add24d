import Image from 'next/image'
import Link from 'next/link'
import React from 'react'

const Logo = () => {
  return (
    <Link className="mt-5.5 inline-block" href={`${process.env.AUTH_URL}/`}>
        <Image
            className="hidden dark:block"
            src={"/logo/logo-dark.svg"}
            alt="Logo"
            width={250}
            height={150}
        />
        <Image
            className="dark:hidden"
            src={"/logo/logo-light.svg"}
            alt="Logo"
            width={250}
            height={150}
        />
    </Link>
  )
}

export default Logo