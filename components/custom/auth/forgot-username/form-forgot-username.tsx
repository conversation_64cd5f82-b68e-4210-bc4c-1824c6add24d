"use client";
import React, { useState } from "react";
import { Mail } from "lucide-react";
import { useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/ui/buttons/button";
import { zodResolver } from "@hookform/resolvers/zod";
import RememberPasswordFormLink from "../other/formlink-remember-password";
import { ForgotUsernameFormData, ForgotUsernameSchema } from "@/schemas/auth/forgot-username/schema";
import { FormInput } from "@/components/ui/form/form-input";
import { ForgotUsername } from "@/utils/api";
import { FormSuccess } from "../../form-success";
import { FormError } from "../../form-error";
import ForgotPasswordFormLink from "../other/formlink-forgot-password";
import { useQuery, useMutation} from '@tanstack/react-query'
import Spinner from "@/components/ui/spinner";

const ForgotUsernameForm = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
    reset,
  } = useForm<ForgotUsernameFormData>({
    resolver: zodResolver(ForgotUsernameSchema),
  });

  const mutation = useMutation({
    mutationFn: ForgotUsername,
    onSuccess: (data) => {
      if (data.code == 1) {
        reset();
      }
    }
  })

  const onSubmit = async (values: ForgotUsernameFormData) => {
    mutation.mutate(values)
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} method="POST">
      <div className="relative mb-6">
        <FormInput
          className=""
          type="email"
          id="email"
          placeholder="<EMAIL>"
          defaultValue=""
          label="Email"
          error={errors.email}
          {...register("email")}
        />
        <span className="absolute right-4 top-15 text-black/50">
          <Mail className="h-5 w-5" />
        </span>
      </div>
      <div className="mt-12 mb-6">
        <Button 
          size="lg" 
          className="w-full" 
          type="submit"
           disabled={mutation.isPending} // Disable button while submitting
          >
            Send Username as Email
        </Button>
      </div>
      <Spinner show={mutation.isPending} />
      {mutation.isSuccess && (
        <FormSuccess message="We have sent your username to your email address!" />
      )}
      {mutation.isError && (
        <FormError message="Contact our support team, this email is not registered!" />
      )}
      <div className="h-4" />
      <ForgotPasswordFormLink />
      <RememberPasswordFormLink />
    </form>
  );
};

export default ForgotUsernameForm;