import React from "react";
import Image from "next/image";
import Card<PERSON>eader, { SubHeaderVariant } from "@/components/custom/header/header-card";
import LogoDark from "../logo/logo-dark";

interface AuthLayoutProps {
    children: React.ReactNode
    header: string
    subheader?: string
    image?: string
}

const AuthLayout = ({ header, subheader, image, children}: AuthLayoutProps) => {
  return (
    <div className="w-full h-screen text-black">
      <div className="p-10 xl:mx-5">
        <LogoDark/>
      </div>
      <div className="flex border-stroke/10">
        <div className="w-full xl:w-1/2 justify-center">
          <div className="rounded-2xl border border-stroke shadow-2 mx-3.5 my-1 xl:mx-15 xl:my-1 bg-white">
            <div className="w-full p-4 sm:px-5 sm:py-10.5 xl:px-8 xl:py-10">
              <CardHeader 
                header = { header }
                subheader = { subheader }  
                subheaderVariant= { SubHeaderVariant.body5 }
              />
              { children }
            </div>
          </div>
        </div>
        <div className="hidden w-full xl:block xl:w-1/2">
          <div className="flex w-full justify-center">
            { image &&
                <Image
                    src = { image }
                    alt = "Logo"
                    width = { 800 }
                    height = { 700 }
                />
            }
          </div>
        </div>
      </div>
    </div>
  )
}

export default AuthLayout