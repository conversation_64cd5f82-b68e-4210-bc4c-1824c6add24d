"use client"
import React, { useState } from 'react'
import { Mail } from 'lucide-react'
import { useForm }  from "react-hook-form"
import { Button } from '@/components/ui/buttons/button'
import { zodResolver } from '@hookform/resolvers/zod'
import { FormInput } from '@/components/ui/form/form-input'
import { ForgotPasswordFormData, ForgotPasswordSchema } from '@/schemas/auth/forgot-password/schema'
import ForgotUsernameFormLink from '../other/formlink-forgot-username'
import RememberPasswordFormLink from '../other/formlink-remember-password'
import { ForgotPassword } from '@/utils/api'
import { FormSuccess } from '../../form-success'
import { FormError } from '../../form-error'
import { useMutation } from '@tanstack/react-query'
import Spinner from '@/components/ui/spinner'

const ForgotPasswordForm = () => {
    const {
        register,
        handleSubmit,
        formState: { errors },
        setError,
        reset,
    } = useForm<ForgotPasswordFormData>({
        resolver: zod<PERSON>esolver(ForgotPasswordSchema)
    })

    const mutation = useMutation({
        mutationFn: ForgotPassword,
        onSuccess: (data) => {
          if (data.code == 1) {
            reset();
          }
        }
    })

    const onSubmit = async (values: ForgotPasswordFormData) => {
        mutation.mutate(values)
    }

    return (
        <form onSubmit={handleSubmit(onSubmit)} method='POST'>
            <div className="relative mb-6">
                <FormInput
                    className=""
                    type="text"
                    id="email"
                    placeholder="<EMAIL>"
                    defaultValue=""
                    label="Email"
                    error={errors.email}
                    {...register("email")}
                />
                <span className="absolute right-4 top-15 text-black/50">
                    <Mail className="h-5 w-5" />
                </span>
            </div>
            <div className="mt-12 mb-6">
                <Button 
                    size="lg" 
                    className="w-full" 
                    type="submit"
                    disabled={mutation.isPending} // Disable button while submitting
                >
                    Send Password Reset Link
                </Button>
            </div>
            <Spinner show={mutation.isPending} />
            { mutation.isSuccess &&
                <FormSuccess message='We have sent you a reset link to your email address!' />    
            }
            { mutation.isError &&
                <FormError message='Contact our support team, this email is not registered!' />    
            }
            <div className='h-4'/>
            <RememberPasswordFormLink />
            <ForgotUsernameFormLink />
        </form>
    )
}

export default ForgotPasswordForm