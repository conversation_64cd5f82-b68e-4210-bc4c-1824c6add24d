"use client";
import React, { Suspense, useRef, useState } from "react";
import { Eye, EyeOff } from "lucide-react";
import { useForm } from "react-hook-form";
import { <PERSON><PERSON> } from "@/components/ui/buttons/button";
import { zodResolver } from "@hookform/resolvers/zod";
import { FormInput } from "@/components/ui/form/form-input";
import { FormErrors } from "@/components/ui/form/form-errors";
import ForgotUsernameFormLink from "../other/formlink-forgot-username";
import RememberPasswordFormLink from "../other/formlink-remember-password";
import {
  ResetPasswordFormData,
  ResetPasswordSchema,
} from "@/schemas/auth/reset-password/schema";
import { ResetPassword } from "@/utils/api";
import { useRouter, useSearchParams } from "next/navigation";
import { FormSuccess } from "../../form-success";
import { FormError } from "../../form-error";
import { useMutation } from "@tanstack/react-query";
import Spinner from "@/components/ui/spinner";

const ResetPasswordForm = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  let token = searchParams.get("token");

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const [code, setCode] = useState<string>("");
  const inputCodeRefs = useRef<(HTMLInputElement | null)[]>([]);

  //TODO: handle the token in POST header

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
    reset,
  } = useForm<ResetPasswordFormData>({
    resolver: zodResolver(ResetPasswordSchema),
  });

  const mutation = useMutation({
      mutationFn: ResetPassword,
      onSuccess: (data) => {
          if(data.code == 1){
            reset();
            router.push("/signin");
          }
      }
  })

  const onSubmit = async (values: ResetPasswordFormData) => {
    mutation.mutate(values)
  };

  //TODO: Reset password with otp based verification

  const handleCodeChange = (index: number, value: string) => {
    const newCode = code.split("");
    newCode[index] = value;
    if (value && index < 5) {
      inputCodeRefs.current[index + 1]?.focus();
    } else if (value && index === 5) {
    }
    setCode(newCode.join(""));
  };

  return (
    <Suspense fallback={<>Loading...</>}>
      <form onSubmit={handleSubmit(onSubmit)} method="POST">
        <div className="mb-6 space-y-2">
          <div className="flex items-center gap-4.5">
            {Array.from({ length: 6 }).map((_, index) => (
              <input
                key={index}
                type="text"
                value={code[index] || ""}
                maxLength={1}
                {...register("code")}
                ref={(val) => {
                  inputCodeRefs.current[index] = val;
                }}
                onChange={(e) => handleCodeChange(index, e.target.value)}
                className="w-[30%] capitalize rounded-md border-[1.5px] border-stroke bg-transparent p-3 text-center text-2xl font-medium text-black outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:text-white dark:focus:border-primary"
              />
            ))}
          </div>
          {errors.code && code.length !== 6 && (
            <FormErrors id="error" errors={errors.code.message} />
          )}
        </div>
        <div className="relative mb-6 hidden">
          <FormInput
            id="code"
            label="Code"
            value={code}
            {...register("code")}
          />
        </div>
        <div className="relative mb-6">
          <FormInput
            type={!showPassword ? "password" : "text"}
            id="password"
            placeholder="*******"
            label="New Password"
            error={errors.password}
            {...register("password")}
          />
          <span
            className="absolute right-4 top-15 text-black/50"
            onClick={() => setShowPassword(!showPassword)}
          >
            {!showPassword ? (
              <Eye className="h-5 w-5" />
            ) : (
              <EyeOff className="h-5 w-5" />
            )}
          </span>
        </div>
        <div className="relative mb-6">
          <FormInput
            type={!showConfirmPassword ? "password" : "text"}
            id="confirmPassword"
            placeholder="*******"
            label="Confirm Password"
            error={errors.confirmPassword}
            {...register("confirmPassword")}
          />
          <span
            className="absolute right-4 top-15 text-black/50"
            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
          >
            {!showConfirmPassword ? (
              <Eye className="h-5 w-5" />
            ) : (
              <EyeOff className="h-5 w-5" />
            )}
          </span>
        </div>
        <div className="mt-12 mb-6">
         <Button 
              size="lg" 
              className="w-full" 
              type="submit"
              disabled={mutation.isPending} // Disable button while submitting
          >
            Submit
          </Button>
        </div>

        <Spinner show={mutation.isPending} />
                    
        { mutation.isSuccess && <FormSuccess message="Password has been changed!" />}
        { mutation.isError && (
          <FormError message="Invalid code, cannot authorize you to change the password. Try again or contact our support team." />
        )}

        <RememberPasswordFormLink />
        <ForgotUsernameFormLink />
      </form>
    </Suspense>
  );
};

export default ResetPasswordForm;
