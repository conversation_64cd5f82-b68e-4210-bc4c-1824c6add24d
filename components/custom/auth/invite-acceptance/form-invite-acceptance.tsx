"use client"
import { <PERSON><PERSON> } from '@/components/ui/buttons/button'
import React, { useRef, useState } from 'react'
import { useForm }  from "react-hook-form"
import ForgotUsernameFormLink from '../other/formlink-forgot-username'
import RememberPasswordFormLink from '../other/formlink-remember-password'
import { zodResolver } from '@hookform/resolvers/zod'
import { FormInput } from '@/components/ui/form/form-input'
import { InviteAcceptanceFormData, InviteAcceptanceSchema } from '@/schemas/auth/invite-acceptance/schema'
import { Eye, EyeOff, Lock, User } from 'lucide-react'
import { useRouter, useSearchParams } from 'next/navigation'
import { AcceptInvite } from '@/utils/api'
import { FormError } from '../../form-error'
import { FormSuccess } from '../../form-success'
import { useMutation } from '@tanstack/react-query'
import Spinner from '@/components/ui/spinner'

const InviteAcceptanceForm = () => {
    const router = useRouter()
    const searchParams = useSearchParams()
    
    const [showPassword, setShowPassword] = useState(false)
    const [showConfirmPassword, setShowConfirmPassword] = useState(false)

    const [error, setError] = useState<string>("")
    const [success, setSuccess] = useState<string>("")

    const {
        register,
        handleSubmit,
        formState: { errors },
        reset,
    } = useForm<InviteAcceptanceFormData>({
        resolver: zodResolver(InviteAcceptanceSchema),
        defaultValues:{
            username: '',
            password: '',
            confirm_password: ''
        }
    })

    const mutation = useMutation({
        mutationFn: AcceptInvite,
        onSuccess: (data) => {
            reset();
            if(data.code == 0){
                setError(data.message)
                return
            }
            setSuccess('Welcome onboard!')
            router.push("/sign-in")
        }
    })

    const onSubmit = async (values: InviteAcceptanceFormData) => {
        console.log('clicked.....')
        setError("")
        setSuccess("")
        const token = searchParams.get('token')
        console.log('Token => ' + token)
        if(token){
            mutation.mutate(values)
        } else {
            setError('Something went wrong. Reach out to Lifeguard Team for assistance.')
        }   
    }

    const onEyeIconClick = (type: number) => {
        type == 1 ? setShowPassword(!showPassword) : setShowConfirmPassword(!showConfirmPassword)
    }

    return (
        <form onSubmit={handleSubmit(onSubmit)} method='POST'>
            <div className="relative mb-6">
                <FormInput
                    type="text"
                    id="username"
                    placeholder="john.smith"
                    label="Username"
                    error={errors.username}
                    {...register('username')}
                />
                <span className="absolute right-4 top-15 text-black/50">
                    <User className="h-5 w-5"/>
                </span>
            </div>
            <div className="relative mb-6">
                <FormInput
                    type= { !showPassword ? 'password' : 'text' }
                    id="password"
                    placeholder="*******"
                    label="New Password"
                    error={errors.password}
                    {...register('password')}
                />
                <span className="absolute right-4 top-15 text-black/50" onClick={() => onEyeIconClick(1)}>
                    {   !showPassword ?
                        <Eye className="h-5 w-5" /> : 
                        <EyeOff className="h-5 w-5" />
                    }
                </span>
            </div>
            <div className="relative mb-6">
                <FormInput
                    type= { !showConfirmPassword ? 'password' : 'text' }
                    id="confirm_password"
                    placeholder="*******"
                    label="Confirm Password"
                    error={errors.confirm_password}
                    {...register('confirm_password')}
                />
                <span className="absolute right-4 top-15 text-black/50" onClick={() => onEyeIconClick(2)}>
                    {   !showConfirmPassword ?
                        <Eye className="h-5 w-5" /> : 
                        <EyeOff className="h-5 w-5" />
                    }
                </span>
            </div>
            <div className="mt-12 mb-6">
                <Button 
                    size="lg" 
                    className="w-full" 
                    type="submit"
                    disabled={mutation.isPending} // Disable button while submitting
                >
                    Accept
                </Button>
            </div>
            <Spinner show={mutation.isPending} />

            <FormError message={error}/>
            <FormSuccess message={success}/>

            <RememberPasswordFormLink />
            <ForgotUsernameFormLink />
        </form>
    )
}

  
export default InviteAcceptanceForm  