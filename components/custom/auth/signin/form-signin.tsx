"use client";
import { <PERSON><PERSON> } from "@/components/ui/buttons/button";
import React, { useState } from "react";
import ForgotPasswordFormLink from "../other/formlink-forgot-password";
import ForgotUsernameFormLink from "../other/formlink-forgot-username";
import HelpCenterFormLink from "../other/formlink-help-center";
import { FormInput } from "@/components/ui/form/form-input";
import { User, EyeOff, Eye, Variable } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  SignInInputFormData,
  SignInSchema,
} from "@/schemas/auth/signin/schema";
import { FormError } from "../../form-error";
import { FormSuccess } from "../../form-success";
import { useRouter } from "next/navigation";
import { useMutation } from "@tanstack/react-query";
import Spinner from "@/components/ui/spinner";
import { Login } from "@/utils/api";
import { ACCESS_TOKEN, REFRESH_TOKEN, setArrayCookie, setCookie, setNumberCookie, USER_ID, USER_NAME, USER_PERMISSIONS, USER_ROLES } from "@/lib/cookie-store";
import { permission } from "process";
import { useSecureRouter } from "@/lib/navigation";

const SignInForm = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState<string>("");
  const [success, setSuccess] = useState<string>("");
  const router = useRouter();

  const { securePush } = useSecureRouter();
  const handleNavigate = () => {
    securePush('/dashboard');
  };


  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
    // setError
  } = useForm<SignInInputFormData>({
    resolver: zodResolver(SignInSchema),
    defaultValues: {
      username: "",
      password: "",
    },
  });

  const mutation = useMutation({
      mutationFn: Login,
      onSuccess: async (response) => {
        setError("");
        let { code, data, error, message } = response
        console.log(data);
        if (code == 0) {
          setError(message + "");
          return;
        }
        if (code == 1) {
          setCookie(ACCESS_TOKEN, data?.tokens?.access_token ?? "", data?.tokens?.expires_in)
          setCookie(REFRESH_TOKEN, data?.tokens?.refresh_token ?? "", data?.tokens?.expires_in)

          //expires_in 30 days
          const roles = data?.roles ?? [];
          const permissions = data?.permissions ?? [];
          setNumberCookie(USER_ID, data?.user?.id ?? 0, 2592000)
          setCookie(USER_NAME, data?.user?.name ?? "", 2592000)
          setArrayCookie(USER_ROLES, roles ?? [], 2592000)
          setArrayCookie(USER_PERMISSIONS, permissions ?? [], 2592000) 


          if(permissions.length ?? 0 > 0){
            // router.push(`/dashboard?type=${permissions[0]}`);
            handleNavigate()
          }
          setSuccess(message + " " + "Hold tight.");
        }
      },
      onError: (error) => {
        setSuccess("");
        setError(error.message);
      },
  })

  const onSubmit = async (values: SignInInputFormData) => {
    mutation.mutate(values)
  };

  const onEyeIconClick = () => {
    setShowPassword(!showPassword);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} method="POST">
      <div className="relative mb-6">
        <FormInput
          className=""
          type="text"
          id="username"
          placeholder="samantha.jose"
          label="Username"
          error={errors.username}
          {...register("username")}
        />
        <span className="absolute right-4 top-15 text-black/50">
          <User className="h-5 w-5" />
        </span>
      </div>
      <div className="relative mb-6">
        <FormInput
          className=""
          type={!showPassword ? "password" : "text"}
          id="password"
          placeholder="******"
          label="Password"
          error={errors.password}
          {...register("password")}
        />
        <span
          className="absolute right-4 top-15 text-black/50"
          onClick={onEyeIconClick}
        >
          {!showPassword ? (
            <Eye className="h-5 w-5" />
          ) : (
            <EyeOff className="h-5 w-5" />
          )}
        </span>
      </div>
      <div className="mt-12 mb-6">
        <Button 
            size="large" 
            className={`w-full ${mutation.isPending ? 'bg-gray-400 ' : ''}`}
            type="submit"
            disabled={mutation.isPending} // Disable button while submitting
        >
          Sign In
        </Button>
      </div>
      <Spinner show={mutation.isPending} />
      
      <FormError message={error} />
      <FormSuccess message={success} />

      <ForgotPasswordFormLink />
      <ForgotUsernameFormLink />
      <HelpCenterFormLink />
    </form>
  );
};

export default SignInForm;
