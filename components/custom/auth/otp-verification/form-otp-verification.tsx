import { Button } from '@/components/ui/buttons/button'
import React, { useRef, useState } from 'react'
import { useForm }  from "react-hook-form"
import ForgotUsernameForm<PERSON>ink from '../other/formlink-forgot-username'
import RememberPasswordFormLink from '../other/formlink-remember-password'
import { OTPVerificationFormData } from '@/schemas/auth/otp-verification/schema'
import { ResetPasswordSchema } from '@/schemas/auth/reset-password/schema'
import { zodResolver } from '@hookform/resolvers/zod'
import { FormErrors } from '@/components/ui/form/form-errors'
import { FormInput } from '@/components/ui/form/form-input'

const OTPVerificationForm = () => {

    //TODO: Sharvari this must be implemented in upcoming release
    const [code, setCode] = useState<string>("")
    const inputCodeRefs = useRef<(HTMLInputElement | null)[]>([])

    const {
        register,
        handleSubmit,
        formState: { errors },
        setError
    } = useForm<OTPVerificationFormData>({
        resolver: zodResolver(ResetPasswordSchema)
    })

    const onSubmit = (values: OTPVerificationFormData) => {
        console.log('Form Submission complete!')
        console.log(values)
    }

    const handleCodeChange = (index: number, value: string) => {
        const newCode = code.split('')
        newCode[index] = value
        if(value && index < 5){
            inputCodeRefs.current[index + 1]?.focus()
        }else  if(value && index === 5){
        }
        setCode(newCode.join(''))
    }

    return (
      <form onSubmit={handleSubmit(onSubmit)} method='POST'>
        <div className='mb-6 space-y-2'>
            <div className="flex items-center gap-4.5">
                {Array.from({ length: 6 }).map((_, index) => (
                    <input
                        key={index}
                        type="text"
                        value={code[index] || ""}
                        maxLength={1}
                        {...register("code")}
                        ref={val => {inputCodeRefs.current[index] = val}}
                        onChange={(e) => handleCodeChange(index, e.target.value)}
                        className="w-[30%] capitalize rounded-md border-[1.5px] border-stroke bg-transparent p-3 text-center text-2xl font-medium text-black outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:text-white dark:focus:border-primary"
                    />
                ))}
            </div>
            {   errors.code && code.length !== 6 && 
                <FormErrors id="error" errors={errors.code.message} />
            }
        </div>
        <div className="relative mb-6 hidden">
            <FormInput
                id="code"
                label="Code"
                value={code}
                // {...inputRef}
                {...register("code")}
            />
        </div>
        <div className="mt-12 mb-6">
            <Button size="lg" className="w-full" type="submit">
            Verify the OTP
            </Button>
        </div>
          <RememberPasswordFormLink />
          <ForgotUsernameFormLink />
      </form>
    )
}

  
export default OTPVerificationForm  