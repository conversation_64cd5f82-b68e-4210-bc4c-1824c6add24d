'use client'
import { ChevronLeft } from 'lucide-react'
import { useRouter } from 'next/navigation'
import React from 'react'

const Back = () => {
    const router = useRouter()   
    return (
        <button className='flex flex-row mb-2.5 h-10 items-center' onClick={() => router.back()}>
            <span><ChevronLeft className='h-8 w-8'/></span>
            <span className='text-title-xsm pt-1'>Back</span>
        </button>
    )
}

export default Back