import React from "react";
import Body from "../typography/body";
import { cn } from "@/lib/utils";
import Header from "../typography/header";

export enum HeaderVariant {
  header1 = "header1",
  header2 = "header2",
  header3 = "header3",
  header4 = "header4",
  header5 = "header5",
  header6 = "header6",
}

export enum SubHeaderVariant {
  body1 = "body1",
  body2 = "body2",
  body3 = "body3",
  body4 = "body4",
  body5 = "body5",
  body6 = "body6",
}

interface CardHeaderProps {
  header: string;
  subheader?: string;
  headerVariant?: HeaderVariant;
  subheaderVariant?: SubHeaderVariant;
  className?: string;
  classNameHeaderVariant?: string;
  classNameSubHeaderVariant?: string;
}

const CardHeader = ({
  header,
  subheader,
  headerVariant = HeaderVariant.header2,
  subheaderVariant = SubHeaderVariant.body5,
  className,
  classNameHeaderVariant,
  classNameSubHeaderVariant,
}: CardHeaderProps) => {
  return (
    <div className={cn(`flex flex-col gap-2 mb-9`, className)}>
      <Header
        variant={headerVariant}
        font="bold"
        className={`text-black ${classNameHeaderVariant}`}
      >
        {header}
      </Header>
      {subheader != null && (
        <Body
          variant={subheaderVariant}
          font="regular"
          className={`text-neutral-500 ${classNameSubHeaderVariant}`}
        >
          {subheader}
        </Body>
      )}
    </div>
  );
};

export default CardHeader;
