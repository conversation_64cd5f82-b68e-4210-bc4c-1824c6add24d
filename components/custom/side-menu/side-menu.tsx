"use client";

import React, { useEffect, useRef, useState } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import SidebarLinkGroup from "./side-menu-group";
import { ArrowLeft, ChevronDown, LogOut } from "lucide-react";
import { cn } from "@/lib/utils";
import { routes } from "./menu-list";
import Image from "next/image";
import { Button } from "@/components/ui/buttons/button";

interface SidebarProps {
  sidebarOpen: boolean
  setSidebarOpen: (arg: boolean) => void
  isDefault: Boolean
  modalOpen: Boolean
  setModalOpen: (arg: boolean) => void
}

const Sidebar = ({ sidebarOpen, setSidebarOpen, isDefault, modalOpen, setModalOpen }: SidebarProps) => {
  const pathname = usePathname();

  const trigger = useRef<any>(null);
  const sidebar = useRef<any>(null);

  let storedSidebarExpanded = "true";

  const [sidebarExpanded, setSidebarExpanded] = useState(
    storedSidebarExpanded === null ? false : storedSidebarExpanded === "true"
  );

  // close on click outside
  useEffect(() => {
    const clickHandler = ({ target }: MouseEvent) => {
      if (!sidebar.current || !trigger.current) return;
      if (
        !sidebarOpen ||
        sidebar.current.contains(target) ||
        trigger.current.contains(target)
      )
        return;
      setSidebarOpen(false);
    };
    document.addEventListener("click", clickHandler);
    return () => document.removeEventListener("click", clickHandler);
  });

  // close if the esc key is pressed
  useEffect(() => {
    const keyHandler = ( event : KeyboardEvent) => {
      if (!sidebarOpen || event.key !== "Escape") return;
      setSidebarOpen(false);
    };
    document.addEventListener("keydown", keyHandler);
    return () => document.removeEventListener("keydown", keyHandler);
  });

  useEffect(() => {
    localStorage.setItem("sidebar-expanded", sidebarExpanded.toString());
    if (sidebarExpanded) {
      document.querySelector("body")?.classList.add("sidebar-expanded");
    } else {
      document.querySelector("body")?.classList.remove("sidebar-expanded");
    }
  }, [sidebarExpanded]);

  return (
    <aside
      ref={sidebar}
      className={`absolute left-0 top-0 z-9999 flex h-screen w-70 flex-col overflow-y-hidden bg-secondary duration-300 ease-linear lg:static lg:translate-x-0 ${
        sidebarOpen ? "translate-x-0" : "-translate-x-full"
      }`}
    >
      <div className="flex items-center justify-between gap-2 px-6 py-5.5 lg:py-6.5">
        <Link href={`${process.env.AUTH_URL}/dashboard`}>
          <Image
            className=""
            src={"/logo/logo-dark.svg"}
            alt="Logo"
            width={180}
            height={80}
          />
        </Link>

        <button
          ref={trigger}
          onClick={() => setSidebarOpen(!sidebarOpen)}
          aria-controls="sidebar"
          aria-expanded={sidebarOpen}
          className="block lg:hidden text-white"
        >
          <ArrowLeft className="h-5 w-5" />
        </button>
      </div>

      <div className="no-scrollbar flex flex-col overflow-y-auto duration-300 ease-linear">
        <nav className="mt-5 px-4 py-4 lg:mt-9 lg:px-6">
          <div>
            <h3 className="mb-4 ml-4 text-sm font-semibold text-neutrals">
              MENU
            </h3>

            <ul className="mb-6 flex flex-col gap-1.5">
                {routes.map((item, index) => (
                    <SidebarLinkGroup
                        key = {index}
                        activeCondition={
                          pathname === `${item.href}` || pathname.includes(`${item.href}`)
                        }
                    >
                        {(handleClick, open) => {
                            return (
                                <>
                                  { item.label == "Logout" &&
                                    <Button
                                      onClick={(e) => setModalOpen(!modalOpen)}
                                      variant={"warning"}
                                      className="group relative flex items-center justify-start gap-2.5 rounded-lg px-4 py-2 font-medium text-neutrals duration-300 ease-in-out hover:bg-primary-container-highlighter/50 w-full"
                                    >
                                      <item.icon className="h-5 w-5"/>
                                      {item.label}
                                    </Button>
                                  }
                                  { item.label != "Logout" &&
                                    <div>
                                      <Link
                                        href={`${process.env.AUTH_URL}${item.href}`}
                                        className={`group relative flex items-center gap-2.5 rounded-lg px-4 py-2 font-medium text-neutrals duration-300 ease-in-out hover:bg-primary-container-highlighter/50 ${
                                          (pathname === item.href || pathname.includes(item.href)) &&
                                          "bg-primary-container-highlighter"
                                        }`}
                                        onClick={(e) => {
                                            if(item.subroutes.length > 0){
                                                e.preventDefault();
                                                sidebarExpanded
                                                    ? handleClick()
                                                    : setSidebarExpanded(true);
                                                }
                                            }
                                        }
                                      >
                                        <item.icon className="h-5 w-5"/>
                                        {item.label}
                                        {item.subroutes?.length > 0 && 
                                            <ChevronDown className={cn("absolute right-4 w-4 h-4", open && "rotate-180")}/>
                                        }
                                    </Link>
                                  {item.subroutes?.length > 0 && 
                                      <div
                                          className={`translate transform overflow-hidden ${
                                            !open && "hidden"
                                          }`}
                                      >
                                          <ul className="mt-4 flex flex-col gap-2.5 pl-6">
                                            {item.subroutes?.length > 0 && (item.subroutes.map((subroute, idx) => (
                                                <li key = {idx}>
                                                    <Link
                                                    href={`${process.env.AUTH_URL}${subroute.href}`}
                                                    className={`group relative flex items-center gap-2.5 rounded-lg px-4 py-2 font-medium text-primary-container-text duration-300 ease-in-out hover:bg-graydark ${
                                                        pathname === subroute.href && "text-white"
                                                    }`}
                                                    >
                                                        <subroute.icon className='w-4 h-4'/>
                                                        {subroute.label}
                                                    </Link>
                                                </li>
                                              
                                            )))}

                                          </ul>
                                      </div>
                                  }
                                    </div>
                                  }
                                </>
                            );
                        }}
                    </SidebarLinkGroup>
                ))}
            </ul>
          </div>
        </nav>
      </div>
    </aside>
  );
};

export default Sidebar;
