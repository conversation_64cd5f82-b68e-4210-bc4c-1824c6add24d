import { Fingerprint, Handshake, Home, LayoutDashboard, LogOut,  Settings, User, UserRoundPlus } from "lucide-react";

export const routes = [
    {
        label: "Home",
        icon: Home,
        href: "/home",
        subroutes: []
    },
    {
        label: "Users",
        icon: User,
        href: "/user",
        subroutes: [
            {
                label: "Info",
                icon: Handshake,
                href: "/user/info"
            },
            {
                label: "Invite",
                icon: UserRoundPlus,
                href: "/user/invite"
            },
        ]
    },
    {
        label: "Settings",
        icon: Settings,
        href: "/settings",
        subroutes: [
            {
                label: "My Profile",
                icon: User,
                href: "/settings/my-profile"
            },
            {
                label: "Change Password",
                icon: Fingerprint,
                href: "/settings/change-password"
            },
        ]
    },
    {
        label: "Logout",
        icon: LogOut,
        href: "/logout",
        subroutes: []
    },
]
