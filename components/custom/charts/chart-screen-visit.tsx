import { ColorPalette } from '@/lib/theme';
import { ApexOptions } from 'apexcharts';
import React, { useState } from 'react'
const ReactApexChart = dynamic(()=> import('react-apexcharts'), {
  ssr:false,
})
import Dashboard<PERSON>ardHeader from "../header/ header-dashboard-card";
import CardHeader, { HeaderVariant, SubHeaderVariant } from '../header/header-card';
import dynamic from 'next/dynamic';

let axisValues: any[] = []  

interface ScreenVisitChartState {
  series: { name: string; data: { x: string, y: number }[] }[];
}

let options: ApexOptions = {
    colors: ColorPalette,
    chart: {
        fontFamily: "Metropolis, Regular",
        type: "bar",
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: "25%",
        borderRadius: 0,
      },
    },
    dataLabels: {
      enabled: false,
    },
    xaxis: {
      categories: Object.keys(axisValues),
      axisBorder: {
        show: false,
      },
      axisTicks: {
        show: false,
      },
    },
    legend: {
      show: true,
      position: "top",
      horizontalAlign: "left",
      markers: {
        size: 99,
      },
    },
    grid: {
      strokeDashArray: 7,
    },
};

interface ScreenVisitChartProps{
  data: any[]
}

const ScreenVisitChart = ({ data }: ScreenVisitChartProps) => {
  const seriesData = data.map(item => {
      return { x: `${item.page}`, y: item.count }
  })

  const [state, setState] = useState<ScreenVisitChartState>({
    series:[{ name: "dasdas", data: seriesData }]
  });

  // Update the state
  const updateState = () => {
    setState((prevState) => ({
      ...prevState,
    }));
  };
  updateState;

  return (
    <div className="col-span-12 rounded-2xl border border-stroke bg-white shadow-3 xl:col-span-5">
      <div className="relative z-0 w-full">
            <DashboardCardHeader title="Top Visited Screens" description='Short explanation for the client.'/>
            <div className="absolute inset-0 flex justify-end items-center z-10 pr-7.5">
                <CardHeader 
                    header={data.reduce((acc, item) => acc  + parseInt(item.count), 0)}
                    subheader="Total Views"
                    headerVariant={ HeaderVariant.header6 }
                    subheaderVariant={ SubHeaderVariant.body6 }
                    className='mb-0 text-end'
                    classNameHeaderVariant='text-primary'
                />
            </div>
        </div>

      <div className="px-6 py-7.5">
        <div id="chartFive" className="-ml-5">
          <ReactApexChart
            options={options}
            series={state.series}
            type="bar"
            height={350}
          />
        </div>
      </div>
    </div>
  );
}

export default ScreenVisitChart