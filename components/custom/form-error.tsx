import { ShieldAlert } from "lucide-react"

interface FormErrorProps {
    message?: string
}

export const FormError = ({ message } : FormErrorProps) => {
    if (!message) return null
    return (
        <div className="bg-red-50 p-3 rounded-md flex items-center gap-x-2 text-sm text-red-800">
            <ShieldAlert className="h-4 w-4"/>
            <p className="text-red-800 body6 font-extrabold">{message}</p>
        </div>
    )
}