import React, { useEffect, useState } from 'react'
import CardDataStats from '../../../card/card-data-stats';
import { ColorPalette, ColorPaletteWithBackground, THEME, Theme, localThemes } from '@/lib/theme';
import { cn } from '@/lib/utils';
import { Palette } from '@/model/color-palette';
import {  Eye, Timer, User, Users2 } from "lucide-react"
import { useFirebaseData } from '@/contexts/fire-base-context';
import Label from '@/components/custom/typography/label';







type Card = {
  title: string;
  total: any; // Replace 'any' with the actual type if possible
  icon: React.ComponentType<{ className?: string; style?: React.CSSProperties }>;
};

interface DashboardCardData {
  initialCards: Card[];
  colors?: { color: string; background: string }[];
  firebaseKeyMapping: Record<string, { keys: string[], index: number }>;
}

const DashboardConnectCardsLayout = ({ initialCards, colors = ColorPaletteWithBackground, firebaseKeyMapping }: DashboardCardData) => {
  const firebaseData = useFirebaseData();
  const [cards, setCards] = useState<Card[]>(initialCards);

  useEffect(() => {
    console.log('Fetched Firebase Data:', firebaseData); // Log the fetched data

    if (firebaseData && firebaseData[0]?.data) {
      setCards(prevState =>
        prevState.map((card: Card) => {
          const mapping = firebaseKeyMapping[card.title];
          if (!mapping) return card;

          const { keys, index } = mapping;

          const firebaseValue = keys.reduce((sum, key) => {
            const value = firebaseData[0].data[key]?.[index];
            return sum + (value ? parseFloat(value) : 0);
          }, 0);

          return { ...card, total: Math.round(firebaseValue) };
        })
      );
    }
  }, [firebaseData, firebaseKeyMapping]);

  return (
    <>
      {cards.map((item, index) => (
        <CardDataStats title={item.title} total={item.total || "0"} key={item.title}>
          <span className={cn(`rounded-full p-3`)} style={{ background: colors[index]?.background }}>
            <item.icon className={`h-6 w-6`} style={{ color: colors[index]?.color }} />
          </span>
        </CardDataStats>
      ))}
    </>
  );
};

export default DashboardConnectCardsLayout;