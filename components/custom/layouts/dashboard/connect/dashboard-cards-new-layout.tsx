import React, { useEffect, useState } from 'react'
import CardDataStats from '../../../card/card-data-stats';
import { ColorPalette, ColorPaletteWithBackground, THEME, Theme, localThemes } from '@/lib/theme';
import { cn } from '@/lib/utils';
import { Palette } from '@/model/color-palette';

interface DashboardCardData {
    data: any[],
    colors?: Palette[]
}

const DashboardCardsNewLayout = ({ data, colors = ColorPaletteWithBackground }: DashboardCardData) => {
  return (
    <>
      {data.map((item, index) => (
        <CardDataStats
          title={item.title}
          total={item.total || "0"}
          key={item.title}
          view={item.view}
          page={item.page}
        >
          <span className={cn(`rounded-full p-3`)} style={{ background: colors[index].background }}>
            <item.icon className={`h-6 w-6`} style={{ color: colors[index].color }} />
          </span>
        </CardDataStats>
      ))}
    </>
  );
};

export default DashboardCardsNewLayout;

