import { useCsvData } from '@/contexts/csv-context'
import { DateContext } from '@/contexts/date-context'
import React, { useContext, useEffect, useState } from 'react'
import CardDataStats from '../../../card/card-data-stats';
import { ColorPalette, ColorPaletteWithBackground, THEME, Theme, localThemes } from '@/lib/theme';
import { cn } from '@/lib/utils';

type Card = {
    title: string;
    total: any; // Ensure total is always a number
    icon: React.ComponentType<{ className?: string; style?: React.CSSProperties }>;
  };
  
  interface DashboardCardData {
    initialCards: Card[];
    colors?: { color: string; background: string }[];
    csvKeyMapping: Record<string, string>;
  }
  
const DashboardCsvCardsLayout = ({ initialCards, colors = ColorPaletteWithBackground, csvKeyMapping }: DashboardCardData) => {
  const { data: csvData } = useCsvData();
  const { date } = useContext(DateContext);
  const [cards, setCards] = useState<Card[]>(initialCards);

  useEffect(() => {
    if (csvData.length > 0) {
      const filteredData = csvData.filter((row: any) => {
        const rowDate = new Date(row.Date);
        return rowDate >= new Date(date.start) && rowDate <= new Date(date.end);
      });

      const aggregatedData = filteredData.reduce((acc: any, row: any) => {
        Object.keys(csvKeyMapping).forEach(key => {
          const csvKey = csvKeyMapping[key];
          const value = Number(row[csvKey]);
          acc[key] = (Number(acc[key]) || 0) + value;
        });
        return acc;
      }, {});

      setCards(prevState =>
        prevState.map((card: Card) => {
          const csvValue = aggregatedData[card.title] || 0;
          return { ...card, total: Math.round(csvValue) };
        })
      );
    }
  }, [csvData, date, csvKeyMapping]);

  return (
    <>
      {cards.map((item, index) => (
        <CardDataStats title={item.title} total={String(item.total) || "0"} key={item.title}>
          <span className={cn(`rounded-full p-3`)} style={{ background: colors[index]?.background }}>
            <item.icon className={`h-6 w-6`} style={{ color: colors[index]?.color }} />
          </span>
        </CardDataStats>
      ))}
    </>
  );
};

export default DashboardCsvCardsLayout;
