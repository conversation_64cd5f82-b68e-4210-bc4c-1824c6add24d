import DropdownEllipsis from '@/components/custom/dropdown/dropdown-ellipsis';
import DashboardCardHeader from '@/components/custom/header/ header-dashboard-card';
import Label from '@/components/custom/typography/label';
import { ColorPaletteWithBackground } from '@/lib/theme';
import { unique } from 'next/dist/build/utils';
import React, { useEffect, useState } from 'react'
import { useFirebaseData } from "@/contexts/fire-base-context";
const screenNameToDisplayName = {
  // Timer related views
  "Timer": "Timer",
  "classic_TimerView": "Timer",
  "métis_identified_TimerView": "Timer",
  "classic_TimerFaqView": "Timer",
  "métis_identified_TimerFaqView": "Timer",
  "classic_TimerInstructionView": "Timer",
  "métis_identified_TimerInstructionView": "Timer",
  "classic_TermsView": "Timer",
  "métis_identified_TermsView": "Timer",

  // Main/Home views
  "Main": "Main",
  "classic_HomeView": "Main",
  "métis_identified_HomeView": "Main",
  "HomeView": "Main",

  // Splash screens
  "Splash": "Splash",
  "classic_SplashView": "Splash",
  "métis_identified_SplashView": "Splash",
  "classique_SplashView": "Splash",

  // Profile related
  "Profile": "Profile",
  "classic_SelectThemeView": "Profile",
  "métis_identified_SelectThemeView": "Profile",
  "SelectThemeView": "Profile",

  // Welcome/Onboarding
  "Welcome": "Welcome",
  "classic_WelcomeView": "Welcome",
  "métis_identified_VerifyPhoneView": "Welcome",
  "classic_VerifyPhoneView": "Welcome",

  // Services Near Me
  "SNM": "Services Near Me",
  "classic_SNMView": "Services Near Me",
  "métis_identified_SNMView": "Services Near Me",
  "classic_SNMFilterView": "Services Near Me",
  "métis_identified_SNMFilterView": "Services Near Me",

  // Emergency/Resources
  "Emergency": "Emergency",
  "classic_EmergencyView": "Emergency",
  "métis_identified_EmergencyView": "Emergency",
  "Guide": "Emergency",
  "classic_ResourcesView": "Resources",
  "métis_identified_ResourcesView": "Resources",
  "classic_AllResourcesView": "Resources",
  "métis_identified_AllResourcesView": "Resources",
  "ResourcesView": "Resources",

  // Web/Address views
  "WebView": "WebView",
  "classic_EditAddressView": "Address",
  "métis_identified_EditAddressView": "Address",

  // Alerts
  "Alerts": "Alerts",
  "AlertsView": "Alerts",
  "classic_AlertsView": "Alerts",
  "métis_identified_AlertsView": "Alerts",

  // Other categories
  "Categories": "Categories",
  "FeedNotification": "Notifications",
  "classic_InMemoryView": "In Memory",
  "InMemoryView": "In Memory",
  "classic_ContactView": "Contact Us",
  "ErroView": "Error",
  "classic_ErroView": "Error",
  "métis_identified_ErroView": "Error",
  "classique_ErroView": "Error"
};

const ScreenVisitLayout = () => {
  const firebaseData = useFirebaseData();
  const [data, setData] = useState<{ screen: string; views: number; unique: number; percentage: number }[]>([]);

  useEffect(() => {
    const newData = Object.keys(screenNameToDisplayName).map(key => {
      if (firebaseData[0]?.data && firebaseData[0]?.data[key]) {
        const views = parseInt(firebaseData[0].data[key][0]);
        const unique = parseInt(firebaseData[0].data[key][1]);
        return {
          screen: screenNameToDisplayName[key as keyof typeof screenNameToDisplayName],
          views,
          unique,
          percentage: 0 // placeholder, will be calculated later
        };
      } else {
        return {
          screen: screenNameToDisplayName[key as keyof typeof screenNameToDisplayName],
          views: 0,
          unique: 0,
          percentage: 0
        };
      }
    });

    // Sort the data in descending order by views and keep only the top 5 items
    const sortedData = newData.sort((a, b) => b.views - a.views).slice(0, 5);

    // Find the maximum views value for normalization
    const maxViews = Math.max(...sortedData.map(item => item.views));

    // Normalize the views to a percentage of the maximum views
    const normalizedData = sortedData.map(item => ({
      ...item,
      percentage: maxViews ? (item.views / maxViews) * 100 : 0
    }));

    setData(normalizedData);
  }, [firebaseData]);

  return (
    <div className="col-span-12 bg-white rounded-2xl border-0 border-neutralsmildGrey border-stroke shadow-md shadow-secondary/25 h-full">
      <div className="relative z-0 w-full">
        <DashboardCardHeader title="Top Visited Screens" description="Which app screens do users visit most" />
        <div className="absolute inset-0 flex justify-end items-center z-10 pr-6">
          <DropdownEllipsis page="/dashboard/lite/temperature" />
        </div>
      </div>

      <div className="flex flex-col gap-2 py-2.5 mb-3.5 px-7.5">
        <div className="grid grid-cols-10 py-2">
          <Label variant="label5" font="medium" className="col-span-6">Screen Name</Label>
          <Label variant="label5" font="medium" className="col-span-2 text-center">Views</Label>
          <Label variant="label5" font="medium" className="col-span-2 text-center">Uniques</Label>
        </div>
        {data.map((item, index) => (
          <div className="relative z-1 grid grid-cols-10 py-2" key={index}>
            <span
              className="absolute left-0 top-0 -z-1 h-full rounded bg-gray-100 dark:bg-meta-4"
              style={{ background: ColorPaletteWithBackground[index].background, width: `${item.percentage}%` }}
            ></span>
            <Label variant="label5" font="bold" className="col-span-6 pl-3.5" style={{ color: ColorPaletteWithBackground[index].color }}>
              {item.screen}
            </Label>
            <Label variant="label5" font="medium" className="col-span-2 text-center">{item.views}</Label>
            <Label variant="label5" font="medium" className="col-span-2 text-center">{item.unique}</Label>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ScreenVisitLayout;