import { Button } from '@/components/ui/buttons/button';
import { FormInput } from '@/components/ui/form/form-input';
import { Download, Filter, Mail } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React, { useContext, useEffect } from 'react';
import DatePicker from '../../datepicker/datepicker';
import { DateProvider, DateContext } from '@/contexts/date-context';
import { AddStat } from "@/utils/api";
import { EVENT_REPORT_DOWNLOAD } from '@/lib/events-store';
import { useCookies } from 'next-client-cookies'
import { getCookie } from '@/lib/cookie-store';
import { ACCESS_TOKEN } from '@/lib/cookie-store';

const DashboardFilterLayout = () => {
  const { date, setDate } = useContext(DateContext);
  const cookies = useCookies();
  const pathname = usePathname();
  

  // Initialize the date range to 30 days ago to today
  useEffect(() => {
    const today = new Date();
    const thirtyDaysAgo = new Date(today);
    thirtyDaysAgo.setDate(today.getDate() - 30);

    const format = (date: Date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0'); // JS months are 0-based, so we add 1
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    }

    setDate({ start: format(thirtyDaysAgo), end: format(today) });
  }, [setDate]);

  const handleDownloadPdf = async (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();
    console.log('[PDF] Starting download...');

    try {
      const statsData = {
        event_name: EVENT_REPORT_DOWNLOAD,
        event_click: true,
        customer_id: 1,
      };

      await AddStat(statsData);

      const token = getCookie(ACCESS_TOKEN) as string;
      console.log('[PDF] Token from cookie:', token);

      const params = new URLSearchParams(window.location.search);
      const permission = params.get('permission');
      console.log('[PDF] Permission from URL:', permission);

      const query = new URLSearchParams();
      if (permission) query.set('permission', permission);

      const fullPath = `${pathname}`;
      const search = query.toString(); 

      const body = {
        url: fullPath,
        token,
        search, 
      };

      console.log('[PDF] Final body to send:', JSON.stringify(body, null, 2));

      const res = await fetch('/api/v1/pdf', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(token ? { Authorization: `Bearer ${token}` } : {}),
        },
        body: JSON.stringify(body),
      });

      console.log('[PDF] Response status:', res.status);

      if (!res.ok) throw new Error(`Failed to fetch PDF: ${res.statusText}`);

      const blob = await res.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `report${pathname?.replace(/\//g, '_')}.pdf`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      console.log('[PDF] PDF download complete.');
    } catch (error) {
      console.error('[PDF] Error downloading PDF:', error);
    }
  };


  const handleDateChange = (dates: Date[]) => {
    if (dates.length === 2) {
      const format = (date: Date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0'); // JS months are 0-based, so we add 1
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      }

      const formattedStartDate = format(dates[0]);
      const formattedEndDate = format(dates[1]);

      console.log(formattedStartDate, formattedEndDate); // Outputs start and end dates in YYYY-MM-DD format

      setDate({ start: formattedStartDate, end: formattedEndDate }); // Update the date in the context
    }
  }

  return (
    <div className='flex flex-row justify-between'>
         <div className='flex gap-4'>
          {/* <Button variant="default" className='font-semibold bg-primary text-white rounded-full hover:bg-primary hover:text-white' >
            <Link href="/dashboard/connect">
              Connect
            </Link>
          </Button>  
        */}
        </div> 

        <div className='flex gap-4'>
          <div className="flex bg-white rounded-3xl border  border-black py-1 px-4 items-center">
            <DatePicker onChange={handleDateChange} />
            <Filter className="h-4 w-4 ml-2"/>
          </div>
          <Button variant="outline" className='bg-white border-black rounded-3xl' onClick={handleDownloadPdf}>
              Download Report <Download className='h-4 w-4 ml-2'/>
          </Button>
        </div>
    </div>
  )
}

export default DashboardFilterLayout;
