import React from 'react'
import { ColorPalette, ColorPaletteWithBackground } from '@/lib/theme';
import <PERSON><PERSON>eader, { HeaderVariant, SubHeaderVariant } from '../../header/header-card';
import Label from '../../typography/label';
import { Palette } from '@/model/color-palette';

interface DashboardCardData {
    data: any[],
    colors?: Palette[]
}

const DashboardCardsLayout1 = ({ data, colors = ColorPaletteWithBackground }: DashboardCardData) => {
  return (
    <>
        {data.map((item, index) => (
           <div className={`flex flex-col text-center justify-center gap-3`}>
            <div className={data.length-1 == index ?  `` : `border-b border-stroke pb-5 xl:border-b-0 xl:border-r xl:pb-0`}>
              <div className="flex items-center justify-center mb-2.5">
                <item.icon className={`h-5 w-5`} style ={{ color: colors[index].color }}/>
              </div>
              <div className='flex flex-col gap-3 mt-3.5 items-center'>
                <Label font={'bold'} className='w-fit px-4.5 py-2.5 rounded-full' style ={{ background: colors[index].background, color: colors[index].color }}>
                  {item.total}
                </Label>
                <Label variant={'label5'} className='font-medium text-slate-600'>
                  {item.title}
                </Label>
              </div>
            </div>
          </div>
        ))}
    </>
  )
}

export default DashboardCardsLayout1

