import { Button } from '@/components/ui/button'
import { FormInput } from '@/components/ui/form/form-input'
import { Download, Filter, Mail } from 'lucide-react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import React from 'react'
import Dropdown from '../../dropdown/dropdown'


const DashboardLiteBuildingLayout = () => {

  const pathname = usePathname()
  
  return (
    <div className='flex flex-row justify-between mt-3'>
        <div className='flex gap-4'>
        </div>
        <div className='flex gap-4'>
          <Dropdown/>
        </div>
    </div>
  )
}

export default DashboardLiteBuildingLayout