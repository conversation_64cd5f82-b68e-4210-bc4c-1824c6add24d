"use client"
import React, { useState } from 'react'
import SideMenu from '../side-menu/side-menu'
import LogoutLayout from '@/app/(index)/_component/logout'

const DefaultLayout = ({
    children
}:{
    children: React.ReactNode
}) => {
    const [sidebarOpen, setSidebarOpen] = useState(false)
    const [modalOpen, setModalOpen] = useState(false)
  return (
    <>
        <div className='flex h-screen overflow-hidden bg-gradient-to-r from-[#EBFDFF]/80 to-[#80D1D7]/50'>
            <SideMenu
                  setSidebarOpen={setSidebarOpen}
                  sidebarOpen={sidebarOpen}
                  isDefault={true}
                   modalOpen={modalOpen} 
                   setModalOpen={setModalOpen}                
            />
            <div className='relative flex flex-1 flex-col overflow-y-auto overflow-x-hidden'>
                {/* <Header setSidebarOpen={setSidebarOpen} sidebarOpen={sidebarOpen} /> */}
                <main className='h-full '>
                    <div className='mx-auto max-w-screen-3xl p-4 md:p-6 2xl:p-10'>
                        {children}
                    </div>
                </main>
            </div>
        </div>
        <LogoutLayout modalOpen={modalOpen} setModalOpen={setModalOpen} />
    </>
  )
}

export default DefaultLayout