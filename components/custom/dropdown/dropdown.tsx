import React, { useState, useRef, useEffect } from "react";
import Link from "next/link";
import { ChevronDown } from "lucide-react";
import { Button } from "@/components/ui/button";

const Dropdown = () => {
  const [dropdownOpen, setDropdownOpen] = useState(false);

  const trigger = useRef<any>(null);
  const dropdown = useRef<any>(null);

  // close on click outside
  useEffect(() => {
    const clickHandler = ({ target }: MouseEvent) => {
      if (!dropdown.current) return;
      if (
        !dropdownOpen ||
        dropdown.current.contains(target) ||
        trigger.current.contains(target)
      )
        return;
      setDropdownOpen(false);
    };
    document.addEventListener("click", clickHandler);
    return () => document.removeEventListener("click", clickHandler);
  });

  // close if the esc key is pressed
  useEffect(() => {
    const keyHandler = ({ keyCode }: KeyboardEvent) => {
      if (!dropdownOpen || keyCode !== 27) return;
      setDropdownOpen(false);
    };
    document.addEventListener("keydown", keyHandler);
    return () => document.removeEventListener("keydown", keyHandler);
  });

  const list = ["Building 1", "Building 2", "Building 3", "Building 4", "Building 5", "Building 6",]


  return (
    <div className="relative inline-block">
        <Button
          ref={trigger}
          onClick={() => setDropdownOpen(!dropdownOpen)}
          className="inline-flex items-center gap-2.5 rounded-full bg-primary-container-highlighter px-5.5 py-3 font-medium text-white hover:bg-primary-container-highlighter hover:bg-opacity-95"
        >
          All Building
          <ChevronDown className={`h-4 w-4 fill-current duration-200 ease-linear ${
              dropdownOpen && "rotate-180"
            }`}/>
        </Button>
        <div
          ref={dropdown}
          onFocus={() => setDropdownOpen(true)}
          onBlur={() => setDropdownOpen(false)}
          className={`absolute left-0 top-full z-40 mt-2 w-full rounded-md border border-stroke bg-white shadow-card dark:border-strokedark dark:bg-boxdark ${
            dropdownOpen === true ? "block" : "hidden"
          }`}
        >
          <ul className="flex flex-col items-start">
            { list.map((item, index) => (
                <li className={`px-5 py-2 font-medium w-full hover:bg-primary-container-highlighter hover:text-white ${index === 0 && 'pt-3'} ${index === list.length-1 && 'pb-3'}`}>
                  {item}
                </li>
              ))
            }
          </ul>
        </div>
    </div>
  );
};

export default Dropdown;
