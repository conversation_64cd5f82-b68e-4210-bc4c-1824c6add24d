import { ChevronDown, X } from "lucide-react";
import React, { useState, useEffect, useRef } from "react";

interface Option {
  value: string
  text: string
  selected: boolean
  element?: HTMLElement
}

interface DropdownProps {
  id: string
  error?: string
  className?: string
  data?: any[],
  label: string
  selectedItems?: () => number[]
}

const MultiSelectDropdown = ({ id, error, className, data, label, selectedItems}: DropdownProps) => {
  const [options, setOptions] = useState<Option[]>([]);
  const [selected, setSelected] = useState<number[]>([]);
  const [show, setShow] = useState(false);
  const dropdownRef = useRef<any>(null);
  const trigger = useRef<any>(null);
  
  useEffect(() => {
    const loadOptions = () => {
      const select = document.getElementById(id) as HTMLSelectElement | null;
      if (select) {
        const newOptions: Option[] = [];
        for (let i = 0; i < select.options.length; i++) {
          newOptions.push({
            value: select.options[i].value,
            text: select.options[i].innerText,
            selected: select.options[i].hasAttribute("selected"),
          });
        }
        setOptions(newOptions);
      }
    };

    loadOptions();

    document.addEventListener("click", clickHandler);
    return () => document.removeEventListener("click", clickHandler);
  }, [data]);

  const open = () => {
    setShow(true);
  };

  const isOpen = () => {
    return show === true;
  };

  const select = (index: number, event: React.MouseEvent) => {
    const newOptions = [...options];

    if (!newOptions[index].selected) {
      console.log("selected if")
      newOptions[index].selected = true;
      newOptions[index].element = event.currentTarget as HTMLElement;
      setSelected([...selected, index]);
    } else {
      console.log("selected else")
      const selectedIndex = selected.indexOf(index);
      if (selectedIndex !== -1) {
        newOptions[index].selected = false;
        setSelected(selected.filter((i) => i !== index));
      }
    }

    setOptions(newOptions);
  };

  const remove = (index: number) => {
    console.log("remove")
    const newOptions = [...options];
    const selectedIndex = selected.indexOf(index);

    if (selectedIndex !== -1) {
      console.log("remove if")
      newOptions[index].selected = false;
      setSelected(selected.filter((i) => i !== index));
      setOptions(newOptions);
    }
  };

  const selectedValues = () => {
    return selected.map((option) => options[option].value);
  };

  const clickHandler = ({ target }: MouseEvent) => {
    if (!dropdownRef.current) return;
    if (
      !show ||
      dropdownRef.current.contains(target) ||
      trigger.current.contains(target)
    )
      return;
    setShow(false);
  };


  return (
    <div className="rleative space-y-2">
      <label className="mb-1.5 block font-medium text-neutral-600">
          {label}
      </label>
      <div>
        <select className="hidden" id={id}>
          { data &&
            data.map((item, index) => {
                return <option key={index} value={item.id}>{item.name}</option>  
            })
          }
         
        </select>

        <div className="flex flex-col items-center">
          <input name="values" type="hidden" defaultValue={selectedValues()} />
          <div className="relative z-20 inline-block w-full">
            <div className="relative flex flex-col items-center">
              <div ref={trigger} onClick={open} className="w-full">
                <div className={`flex w-full rounded-lg border ${error ? 'border-red-700' : 'border-stroke'} appearance-none bg-gray py-3 px-4.5 text-black focus:border-primary focus-visible:outline-none dark:border-strokedark dark:bg-meta-4 dark:text-white dark:focus:border-primary ${className}`}>
                  <div className="flex flex-auto flex-wrap gap-3">
                    {selected.map((index) => (
                      <div
                        key={index}
                        className={`flex items-center w-fit rounded-lg border appearance-none py-1 px-2 text-black`}
                      >
                        <div className="max-w-full flex-initial">
                          {options[index].text}
                        </div>
                        <div className="flex flex-auto flex-row-reverse">
                          <div
                            onClick={() => remove(index)}
                            className="cursor-pointer pl-2 hover:text-danger"
                          >
                            <X className="h-4 w-4"/>
                          </div>
                        </div>
                      </div>
                    ))}
                    {selected.length === 0 && (
                      <div className="flex-1">
                        <input
                          placeholder="Select an option"
                          className="h-full w-full appearance-none bg-transparent p-1 px-2 outline-none"
                          defaultValue={selectedValues()}
                        />
                      </div>
                    )}
                  </div>
                  <div className="flex w-5 items-center">
                    <button
                      type="button"
                      onClick={open}
                      className="h-5 w-5 cursor-pointer outline-none focus:outline-none"
                    >
                      <span className="text-black/50">
                          <ChevronDown className="h-5 w-5" />
                      </span>
                    </button>
                  </div>
                </div>
              </div>
              <div className="w-full">
                <div
                  className={`max-h-select absolute left-0 top-full z-40 w-full overflow-y-auto rounded-lg bg-white shadow dark:bg-form-input ${
                    isOpen() ? "" : "hidden"
                  }`}
                  ref={dropdownRef}
                  onFocus={() => setShow(true)}
                  onBlur={() => setShow(false)}
                >
                  <div className="flex w-full flex-col">
                    {options.map((option, index) => (
                      <div key={index}>
                        <div
                          className="w-full cursor-pointer rounded-t border-b border-stroke hover:bg-primary/5 dark:border-form-strokedark"
                          onClick={(event) => select(index, event)}
                        >
                          <div
                            className={`relative flex w-full items-center border-l-2 border-transparent p-2 pl-2 ${
                              option.selected ? "border-primary" : ""
                            }`}
                          >
                            <div className="flex w-full items-center">
                              <div className="mx-2 leading-6">
                                {option.text}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MultiSelectDropdown;
