import { ChevronR<PERSON>, Pencil } from "lucide-react";
import Link from "next/link";
import { useEffect, useRef, useState } from "react";

interface DropdownEllipsisProps{
  page: string
}

const DropdownEllipsis = ({ page = "" }: DropdownEllipsisProps) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);

  const trigger = useRef<any>(null);
  const dropdown = useRef<any>(null);

  // close on click outside
  useEffect(() => {
    const clickHandler = ({ target }: MouseEvent) => {
      if (!dropdown.current) return;
      if (
        !dropdownOpen ||
        dropdown.current.contains(target) ||
        trigger.current.contains(target)
      )
        return;
      setDropdownOpen(false);
    };
    document.addEventListener("click", clickHandler);
    return () => document.removeEventListener("click", clickHandler);
  });

  // close if the esc key is pressed
  useEffect(() => {
    const keyHandler = ({ keyCode }: KeyboardEvent) => {
      if (!dropdownOpen || keyCode !== 27) return;
      setDropdownOpen(false);
    };
    document.addEventListener("keydown", keyHandler);
    return () => document.removeEventListener("keydown", keyHandler);
  });

  return (
    <div className="relative flex">
      <button
        className="text-[#98A6AD] hover:text-body"
        ref={trigger}
        onClick={() => setDropdownOpen(!dropdownOpen)}
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-ellipsis-vertical"><circle cx="12" cy="12" r="1"/><circle cx="12" cy="5" r="1"/><circle cx="12" cy="19" r="1"/></svg>
      </button>
      <div
        ref={dropdown}
        onFocus={() => setDropdownOpen(true)}
        onBlur={() => setDropdownOpen(false)}
        className={`absolute right-0 top-full z-40 w-40 space-y-1 shadow-3 rounded-lg border border-3 bg-white p-1.5 ${
          dropdownOpen === true ? "block" : "hidden"
        }`}
      >
        <Link href={`${process.env.AUTH_URL}/${page}`}>
          <button className="flex w-full items-center justify-between gap-2 px-4 py-1.5 text-left text-sm hover:bg-gray">
              View More
              <ChevronRight className="h-4 w-4" />
          </button>
        </Link>
      </div>
    </div>
  );
};

export default DropdownEllipsis;
