import { ElementRef, useEffect, useRef, useState } from "react";
import { createPortal } from "react-dom";
import flatpickr from "flatpickr";
import { Button } from "@/components/ui/buttons/button";

import { Instance as Flatpickr } from "flatpickr/dist/types/instance";
import "flatpickr/dist/flatpickr.min.css";

interface DatePickerProps {
  onChange: (date: Date[]) => void;
}

const DatePicker: React.FC<DatePickerProps> = ({ onChange }) => {
  const [flatpickrInstance, setFlatpickrInstance] = useState<flatpickr.Instance>();
  const datePickerRef = useRef<HTMLInputElement>(null);
  const dates = useRef<Date[]>([]);
  const [applyDate, setApplyDate] = useState(() => () => {});

  useEffect(() => {
    const today = new Date();
    const thirtyDaysAgo = new Date(today);
    thirtyDaysAgo.setDate(today.getDate() - 30);

    if (datePickerRef.current) {
      const instance = flatpickr(datePickerRef.current, {
        static: true,
        closeOnSelect: false,
        mode: "range",
        altFormat: "F j, Y",
        dateFormat: "Y-m-d",
        defaultDate: [thirtyDaysAgo, today],
        onChange: (selectedDates) => {
          if (selectedDates.length === 0) {
            onChange([]);
            dates.current = [];
          } else {
            dates.current = selectedDates;
          }

          setApplyDate(() => {
            return () => {
              onChange(dates.current);
              instance.close();
            };
          });
        },
        onClose: () => {
          instance.setDate(dates.current);
        },
      });

      setFlatpickrInstance(instance);

      // Set the initial date range
      dates.current = [thirtyDaysAgo, today];
      onChange(dates.current);
    }

    return () => flatpickrInstance?.destroy();
  }, []);

  return (
    <div className="">
      <input
        className="w-55"
        ref={datePickerRef}
        type="text"
        placeholder="Select date..."
      />
      {flatpickrInstance &&
        createPortal(
          <div className="flex justify-center gap-10 py-2">
            <Button
              className="rounded-full"
              onClick={() => {
                flatpickrInstance.clear(true);
                flatpickrInstance.close();
              }}
            >
              {" "}
              Clear{" "}
            </Button>
            <Button className="rounded-full" onClick={applyDate}>
              Apply
            </Button>
          </div>,
          flatpickrInstance.calendarContainer
        )}
    </div>
  );
};

export default DatePicker;