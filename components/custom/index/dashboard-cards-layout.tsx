import React, { useEffect, useState } from 'react'
import { ColorPaletteWithBackground } from '@/lib/theme';
import { cn } from '@/lib/utils';
import { Palette } from '@/types/color-palette';
import CardDataStats from '../card/card-data-stats';

interface DashboardCardData {
    data: any[],
    colors?: Palette[]
}

const DashboardCardsLayout = ({ data, colors = ColorPaletteWithBackground }: DashboardCardData) => {
  return (
    <>
        {data.map((item, index) => (
            <CardDataStats title={item.title} total={item.total || "0"} key={item.title} view={item.view} page={item.page}>
                <span
                  className="rounded-full p-3 transition-colors duration-200"
                  style={{
                    background: colors[index].background,
                  }}
                  onMouseEnter={(e) => (e.currentTarget.style.background = colors[index].hover)}
                  onMouseLeave={(e) => (e.currentTarget.style.background = colors[index].background)}
                >
                <item.icon className={`h-6 w-6`} style ={{ color: colors[index].color }}/>
              </span>
            </CardDataStats>
        ))}
    </>
  ) 
}
export default DashboardCardsLayout

