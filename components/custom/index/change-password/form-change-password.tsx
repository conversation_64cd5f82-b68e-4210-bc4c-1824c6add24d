"use client";
import React, { use, useState } from "react";
import { useForm } from "react-hook-form";
import SuccessModel from "../../success-model";
import { logout, updatePassword } from "@/services/api";
import { Button } from "@/components/ui/button";
import { Eye, EyeOff, Lock } from "lucide-react";
import { zodResolver } from "@hookform/resolvers/zod";
import { FormInput } from "@/components/ui/form/form-input";
import {
  ChangePasswordFormData,
  ChangePasswordSchema,
} from "@/schemas/auth/change-password/schema";
import { useCurrentUser, useCurrentUserToken } from "@/hooks/use-current-user";
import { useMutation } from "@tanstack/react-query";
import Spinner from "@/components/ui/spinner";

interface ChangePasswordFormProps {
  tokenExpired: Boolean;
  setTokenExpired: (arg: boolean) => void;
}

const ChangePasswordForm = ({
  tokenExpired,
  setTokenExpired,
}: ChangePasswordFormProps) => {
  const token = useCurrentUserToken();
  const [modalOpen, setModalOpen] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
    reset,
  } = useForm<ChangePasswordFormData>({
    resolver: zodResolver(ChangePasswordSchema),
    defaultValues: {
      current_password: "",
      new_password: "",
      confirm_password: "",
    },
  });

  const mutation = useMutation({
    mutationFn: updatePassword,
    onSuccess: async (data) => {
      if (data.status == 1) {
        console.log("Request sent successfully", data);
        reset();
        setModalOpen(true);
      }
    }
  })

  const onSubmit = async (values: ChangePasswordFormData) => {
    console.log("Form Submission complete!");
    console.log(values);
    let response = (await updatePassword(values)) as APIResponse;
    console.log(response);
    if (response.status == 1) {
      console.log("Request sent successfully", response);
      reset();
      setModalOpen(true);
    }
  };
  return (
    <div>
      <form onSubmit={handleSubmit(onSubmit)} method="POST">
        <div className="relative mb-6">
          <FormInput
            className=""
            type={!showCurrentPassword ? "password" : "text"}
            id="current_password"
            placeholder="*******"
            defaultValue=""
            label="Current Password"
            error={errors.current_password}
            {...register("current_password")}
          />
          <span
            className="absolute right-4 top-15 text-black/50"
            onClick={() => setShowCurrentPassword(!showCurrentPassword)}
          >
            {!showCurrentPassword ? (
              <Eye className="h-5 w-5" />
            ) : (
              <EyeOff className="h-5 w-5" />
            )}
          </span>
        </div>
        <div className="relative mb-6">
          <FormInput
            className=""
            type={!showNewPassword ? "password" : "text"}
            id="new_password"
            placeholder="*******"
            defaultValue=""
            label="New Password"
            error={errors.new_password}
            {...register("new_password")}
          />
          <span
            className="absolute right-4 top-15 text-black/50"
            onClick={() => setShowNewPassword(!showNewPassword)}
          >
            {!showNewPassword ? (
              <Eye className="h-5 w-5" />
            ) : (
              <EyeOff className="h-5 w-5" />
            )}
          </span>
        </div>
        <div className="relative mb-6">
          <FormInput
            className=""
            type={!showConfirmPassword ? "password" : "text"}
            id="confirm_password"
            placeholder="*******"
            defaultValue=""
            label="Confirm Password"
            error={errors.confirm_password}
            {...register("confirm_password")}
          />
          <span
            className="absolute right-4 top-15 text-black/50"
            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
          >
            {!showConfirmPassword ? (
              <Eye className="h-5 w-5" />
            ) : (
              <EyeOff className="h-5 w-5" />
            )}
          </span>
        </div>
        <div className="mt-12 mb-6">
          <Button type="submit" className="w-full">
            Update my Password
          </Button>
        </div>
        <Spinner show={ mutation.isPending }/>
      </form>
    </div>
  );
};

export default ChangePasswordForm;
