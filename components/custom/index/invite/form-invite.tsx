"use client"
import React, { useEffect, useState } from 'react'
import { ChevronDown, Mail, User } from 'lucide-react';
import { useForm }  from "react-hook-form"
import { Button } from '@/components/ui/button';
import { zodResolver } from '@hookform/resolvers/zod';
import { FormInput } from '@/components/ui/form/form-input';
import { InviteFormData, InviteSchema } from '@/schemas/auth/invite/schema';
import { FormInputDropdown } from '@/components/ui/form/form-input-dropdown';
import { Building, Organization, Product, Role, Team, Customer } from '@prisma/client';
import SuccessModel from '../../success-model';
import { useCurrentUserToken } from '@/hooks/use-current-user';
import { getBuildingData, getCustomersData, getOrganizationData, getProductData, getRoleData, getTeamData, sendUserInvite } from './form-invite-data';

const InviteForm = () => {
    const token = useCurrentUserToken()
    const [roles, setRoles] = useState<Role[]>([])
    const [teams, setTeams] = useState<Team[]>([])
    const [products, setProducts] = useState<Product[]>([])
    const [customers, setCustomers] = useState<Customer[]>([])
    const [organization, setOrganization] = useState<Organization[]>([])
    const [building, setBuilding] = useState<Building[]>([])

    const [modalOpen, setModalOpen] = useState(false)

    const {
        register,
        handleSubmit,
        formState: { errors },
        setError,
        watch,
        reset
    } = useForm<InviteFormData>({
        resolver: zodResolver(InviteSchema),
        defaultValues:{
            first_name: "",
            last_name: "",
            email: "",
            role_id: 0,
            team_id: 0
        }
    })
  
    const onSubmit = async (values: InviteFormData) => {
        let response: APIResponse = await sendUserInvite(values)
        if(response.message == "Email already in use!" || response.status == 1){
            reset()
            setModalOpen(true)
        }
    }

    useEffect(() => { loadData() }, [])

    useEffect(() => {
        if(watch("product_id") == 3){
            loadOrganizationData()
        }
    }, [watch("product_id")])

    useEffect(() => {
        let id = watch("organization_id") as number
        if(id > 0){
            loadBuildingData()
        }
    }, [watch("organization_id")])


    useEffect(() => {
        let id = watch("organization_id") as number
        if(id > 0){
            loadBuildingData()
        }
    }, [watch("organization_id")])

    const loadData = async () => {
        const roleData = await getRoleData()
        setRoles(roleData)

        const teamsData = await getTeamData()
        setTeams(teamsData)

        const productsData = await getProductData()
        setProducts(productsData)

        const customersData = await getCustomersData({})
        setCustomers(customersData)
    }

    const loadOrganizationData = async () => {
        const organizationData = await getOrganizationData()
        setOrganization(organizationData)
    }

    const loadBuildingData = async () => {
        const buildingData = await getBuildingData(watch("organization_id") as number)
        setBuilding(buildingData)
    }

    return (
        <>
            <form onSubmit={handleSubmit(onSubmit)} method='POST'>
                <div className='grid grid-cols-2 gap-4'>
                    <div className="relative mb-6">
                        <FormInput
                            className=""
                            type="text"
                            id="first_name"
                            placeholder="<EMAIL>"
                            defaultValue=""
                            label="First Name"
                            error={errors.first_name}
                            {...register("first_name")}
                        />
                        <span className="absolute right-4 top-15 text-black/50">
                            <User className="h-5 w-5" />
                        </span>
                    </div>

                    <div className="relative mb-6">
                        <FormInput
                            className=""
                            type="text"
                            id="last_name"
                            placeholder="<EMAIL>"
                            defaultValue=""
                            label="Last Name"
                            error={errors.last_name}
                            {...register("last_name")}
                        />
                        <span className="absolute right-4 top-15 text-black/50">
                            <User className="h-5 w-5" />
                        </span>
                    </div>
                </div>

                <div className="relative mb-6">
                    <FormInput
                        className=""
                        type="email"
                        id="email"
                        placeholder="<EMAIL>"
                        label="Email"
                        error={errors.email}
                        {...register("email")}
                    />
                    <span className="absolute right-4 top-15 text-black/50">
                        <Mail className="h-5 w-5" />
                    </span>
                </div>
                <div className='grid grid-cols-2 gap-4'>
                    <div className="relative mb-6">
                        <FormInputDropdown
                            className=""
                            id="role_id"
                            label="Role"
                            error={errors.role_id}
                            {...register("role_id", {valueAsNumber: true})}                
                        >
                            <option value={0}>Choose a role</option>  
                            {   roles &&
                                roles.map(role => {
                                    return <option key={role.id} value={role.id}>{role.name}</option>  
                                })
                            }
                        </FormInputDropdown>
                        <span className="absolute right-4 top-11 text-black/50">
                            <ChevronDown className="h-5 w-5" />
                        </span>
                    </div>
                    <div className="relative mb-6">
                        <FormInputDropdown
                            className=""
                            id="team_id"
                            label="Team"
                            error={errors.team_id}
                            {...register("team_id", {valueAsNumber: true})}
                        >
                            <option value={0}>Choose a team</option>  
                            {   teams &&
                                teams.map(team => {
                                    return <option key={team.id} value={team.id}>{team.name}</option>  
                                })
                            }
                        </FormInputDropdown>
                        <span className="absolute right-4 top-11 text-black/50">
                            <ChevronDown className="h-5 w-5" />
                        </span>
                    </div>
                </div>

                {   watch("team_id") > 1 && (
                    <div className="relative mb-6">
                        <FormInputDropdown
                            className=""
                            id="product_id"
                            label="Product"
                            error={errors.product_id}
                            {...register("product_id", {valueAsNumber: true})}                
                        >
                            <option value={0}>Choose a product</option>  
                            {   products &&
                                products.map(product => {
                                    return <option key={product.id} value={product.id}>{product.name}</option>  
                                })
                            }
                        </FormInputDropdown>
                        <span className="absolute right-4 top-11 text-black/50">
                            <ChevronDown className="h-5 w-5" />
                        </span>
                    </div>
                )}

                {   watch("product_id") == 1 && (
                    <div className="relative mb-6">
                        <FormInputDropdown
                            className=""
                            id="client_id"
                            label="Client"
                            error={errors.customer_id}
                            {...register("customer_id", {valueAsNumber: true})}                
                        >
                            <option value={0}>Choose a customer</option>  
                            {   customers &&
                                customers.map(customer => {
                                    return <option key={customer.id} value={customer.id}>{customer.name}</option>  
                                })
                            }
                        </FormInputDropdown>
                        <span className="absolute right-4 top-11 text-black/50">
                            <ChevronDown className="h-5 w-5" />
                        </span>
                    </div>
                )}

                {   (watch("product_id") == 3) && (
                    <div className="relative mb-6">
                        <FormInputDropdown
                            className=""
                            id="organization_id"
                            label="Organization"
                            error={errors.organization_id}
                            {...register("organization_id", {valueAsNumber: true})}                
                        >
                            <option value={0}>Choose a organization</option>  
                            {   organization &&
                                organization.map(org => {
                                    return <option key={org.id} value={org.id}>{org.name}</option>  
                                })
                            }
                        </FormInputDropdown>
                        <span className="absolute right-4 top-11 text-black/50">
                            <ChevronDown className="h-5 w-5" />
                        </span>
                    </div>
                )}

                {   watch("product_id") == 3 && (
                    <div className="relative mb-6">
                        <FormInputDropdown
                            className=""
                            id="building_id"
                            label="Building"
                            error={errors.building_id}
                            {...register("building_id", {valueAsNumber: true})}                
                        >
                            <option value={0}>Choose a building</option>  
                            {   building &&
                                building.map(item => {
                                    return <option key={item.id} value={item.id}>{item.name}</option>  
                                })
                            }
                        </FormInputDropdown>
                        <span className="absolute right-4 top-11 text-black/50">
                            <ChevronDown className="h-5 w-5" />
                        </span>
                    </div>
                )}
                

                <div className="mt-12 mb-6">
                    <Button type="submit" className="w-full">
                        Send Invite
                    </Button>
                </div>
            </form>
            <SuccessModel 
                modalOpen={modalOpen} 
                setModalOpen={setModalOpen} 
                title={'Invitation email sent!'} 
                description={'Please ask the client to check their inbox and accept the request within an hour. If the link expires, resend the invite.'} 
                buttonTitle={'Gotcha'}           
            />
        </>
    )
}

export default InviteForm