import { BuildingSchema } from "@/schemas/api/invite/schema";
import { InviteFormData } from "@/schemas/auth/invite/schema";
import {
  building,
  customers,
  Organization,
  products,
  roles,
  sendInvite,
  teams,
} from "@/services/api";

export const getRoleData = async () => {
  const response = (await roles()) as APIResponse;
  if (response.status == 0) {
    return [];
  }
  return response.data;
};

export const getTeamData = async () => {
  const response = (await teams()) as APIResponse;
  if (response.status == 0) {
    return [];
  }
  return response.data;
};

export const getProductData = async () => {
  const response = (await products()) as APIResponse;
  if (response.status == 0) {
    return [];
  }
  return response.data;
};

export const getCustomersData = async (body: any) => {
  const response = (await customers(body)) as APIResponse;
  if (response.status == 0) {
    return [];
  }
  return response.data;
};

export const getOrganizationData = async () => {
  const response = (await Organization()) as APIResponse;
  if (response.status == 0) {
    return [];
  }
  return response.data;
};

export const getBuildingData = async (
  organization_id: number
) => {
  const data: Building = { organization_id: organization_id as number };
  const response = (await building(data)) as APIResponse;
  if (response.status == 0) {
    return [];
  }
  return response.data;
};

export const sendUserInvite = async (data: InviteFormData) => {
  const response = (await sendInvite(data)) as APIResponse;
  return response;
};
