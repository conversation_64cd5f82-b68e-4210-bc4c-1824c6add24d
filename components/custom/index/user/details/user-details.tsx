"use client";
import Image from "next/image";
import { Camera, Edit } from "lucide-react";
import { userDetails } from "@/services/api";
import { Switch } from "@/components/ui/switch";
import React, { useEffect, useState } from "react";
import { useCurrentUser, useCurrentUserToken } from "@/hooks/use-current-user";
import <PERSON><PERSON>ead<PERSON>, {
  SubHeaderVariant,
} from "@/components/custom/header/header-card";
import Back from "@/components/custom/back/back";

interface UserDetailsProps {
  userId: string;
  loading?: Boolean;
  tokenExpired?: Boolean;
  setLoading?: (arg: boolean) => void;
  setTokenExpired?: (arg: boolean) => void;
}

const UserDetails = ({
  userId,
}: UserDetailsProps) => {
  const [userRecord, setUserRecord] = useState<UserDetailsResponse>();
  const [tokenExpired, setTokenExpired] = useState(false)
  const getUserDetails = async () => {
    const record = (await userDetails(
      null,
      userId as string
    )) as APIResponse;
    if (record.status === 0 && record.message == "Token has expired!") {
      setTokenExpired(true);
      return;
    }
    const data: UserDetailsResponse = record.data;
    setUserRecord(data);
  };

  useEffect(() => {
    getUserDetails();
  }, []);

  return (
    <>
      <Back />
      <div className="overflow-hidden rounded-2xl border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
        <div className="relative z-20 h-35 md:h-65">
          <div className="h-full w-full rounded-tl-sm rounded-tr-sm object-cover object-center  bg-[radial-gradient(circle_at_center,_var(--tw-gradient-stops))] from-primary/90 from-40% via-primary/80 via-70% to-primary-container-highlighter">
            <img
              src={"/images/profile-banner.png"}
              alt="profile"
              className="w-[100%] h-[100%] aspect-auto object-cover"
            />
          </div>
          <div className="absolute bottom-1 right-1 z-10 xsm:bottom-4 xsm:right-4">
            <label
              htmlFor="cover"
              className="flex cursor-pointer items-center justify-center gap-2 rounded-full bg-black px-3 py-3 text-sm font-medium text-white hover:bg-opacity-80 xsm:px-4"
            >
              <input type="file" name="cover" id="cover" className="sr-only" />
              <span>
                <Edit className="w-5 h-5" />
              </span>
              <span>Edit</span>
            </label>
          </div>
        </div>
        <div className="px-4 pb-6 text-center lg:pb-8 xl:pb-11.5">
          <div className="relative z-30 mx-auto -mt-22 h-30 w-full max-w-30 rounded-full bg-white/20 p-1 backdrop-blur sm:h-44 sm:max-w-44 sm:p-3">
            <div className="relative drop-shadow-2 rounded-full">
              <Image
                src={
                  (userRecord?.picture as string) ||
                  `/images/profile-pic-missing.png`
                }
                width={160}
                height={160}
                style={{
                  width: "auto",
                  height: "auto",
                }}
                alt="profile"
                className="rounded-full"
              />
              <label
                htmlFor="profile"
                className="absolute bottom-0 right-0 flex h-8.5 w-8.5 cursor-pointer items-center justify-center rounded-full bg-black text-white hover:bg-opacity-90 sm:bottom-2 sm:right-2"
              >
                <Camera className="w-5 h-5" />
                <input
                  type="file"
                  name="profile"
                  id="profile"
                  className="sr-only"
                />
              </label>
            </div>
          </div>
          <div className="mt-4">
            {userRecord?.team.name && (
              <>
                <CardHeader
                  header={`${userRecord?.first_name} ${userRecord?.last_name}`}
                  subheader={`Works @ ${userRecord?.team.name}`}
                  subheaderVariant={SubHeaderVariant.body5}
                />
                <label className="-mt-6 mb-12 flex justify-center">
                  {userRecord?.email_verified == null ? (
                    <p className="text-red-800 bg-red-200 px-3 text-center w-fit rounded-full">
                      Pending
                    </p>
                  ) : (
                    <p className="text-emerald-800 bg-emerald-200 px-3 w-fit text-center rounded-full">
                      Verified
                    </p>
                  )}
                </label>
              </>
            )}

            <div className="mx-auto max-w-180">
              <h4 className="font-semibold text-black dark:text-white">
                About Me
              </h4>
              <div className="mt-4.5 space-y-4">
                <div className="flex justify-between items-center">
                  <p className="font-normal">Email</p>
                  <p className="font-semibold p-2rounded-full">
                    {userRecord?.email}
                  </p>
                </div>
                <div className="flex justify-between items-center">
                  <p className="font-normal">Username</p>
                  <p className="font-semibold p-2rounded-full">
                    {userRecord?.username}
                  </p>
                </div>
                <div className="flex justify-between items-center">
                  <p className="font-normal">Role</p>
                  <p className="font-semibold">{userRecord?.role.name}</p>
                </div>
                <div className="flex justify-between items-center">
                  <p className="font-normal">Team</p>
                  <p className="font-semibold">{userRecord?.team.name}</p>
                </div>
                <div className="flex justify-between items-center">
                  <p className="font-normal">Product Access</p>
                  <p className="font-semibold">
                    {userRecord?.userToProduct?.flatMap(
                      (item) => item.product?.name
                    )}
                  </p>
                </div>
                {userRecord?.userToProduct
                  ?.flatMap((item) => item.product?.name)
                  .includes("Connect") && (
                  <>
                    <div className="flex justify-between items-center">
                      <p className="font-normal">Organization</p>
                      <p className="font-semibold">
                        {userRecord?.userToProduct?.flatMap(
                          (item) => item.customer?.name
                        )}
                      </p>
                    </div>
                  </>
                )}
                {userRecord?.userToProduct
                  ?.flatMap((item) => item.product?.name)
                  .includes("Lite") && (
                  <>
                    <div className="flex justify-between items-center">
                      <p className="font-normal">Organization Access</p>
                      <p className="font-semibold">
                        {userRecord?.userToBuilding?.flatMap(
                          (item) => item.building?.organization?.name
                        )}
                      </p>
                    </div>
                    <div className="flex justify-between items-center">
                      <p className="font-normal">Building Access</p>
                      <p className="font-semibold">
                        {userRecord?.userToBuilding?.flatMap(
                          (item) => item.building?.name
                        )}
                      </p>
                    </div>
                  </>
                )}

                <div className="flex justify-between items-center">
                  <p className="font-normal">Full Access</p>
                  <p className="font-semibold">
                    {userRecord?.userToProduct?.flatMap(
                      (item) => item.full_access
                    ) ?? false === false
                      ? "NO"
                      : "YES"}
                  </p>
                </div>
                <div className="flex justify-between items-center">
                  <p className="font-normal">Two Factor Authentication</p>
                  <p className="font-semibold rounded-full">
                    <label className="inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={userRecord?.is_two_factor_enabled}
                        disabled
                        className="sr-only peer"
                      />
                      <div className="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/30 dark:peer-focus:ring-primary/50 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary"></div>
                    </label>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default UserDetails;
