"use client";
import React, { useEffect, useState } from "react";
import ActionDropdown from "./dropdown/dropdown-action";
import { users } from "@/services/api";
import { useCurrentUser, useCurrentUserToken } from "@/hooks/use-current-user";
import { ACCESS_TOKEN } from "@/lib/cookie-store";
import { useMutation } from "@tanstack/react-query";
import SessionExpiredModel from "../../model-session-expired";

const TableUser = () => {
  const user = useCurrentUser();
  const [tokenExpired, setTokenExpired] = useState(false)
  const [userData, setUserData] = useState<UserDetailsResponse[]>([]);
  
  const mutation = useMutation({
    mutationFn: users,
    onSuccess: async (data) => {
      if (data.status == 1) {
        setUserData(data.data);
      }
    }
  })

  const onLoad = async () => {
    mutation.mutate()
  };

  useEffect(() => {
    onLoad();
  }, []);

  return (
    <>
      <div className="max-w-full h-screen overflow-x-auto">
        <div className="min-w-[1170px] justify-center">
          <div className="grid grid-cols-12 rounded-t-[10px] bg-primary px-5 py-4 lg:px-7.5 2xl:px-11">
            <div className="col-span-2">
              <h5 className="font-medium text-white">Name</h5>
            </div>

            <div className="col-span-3">
              <h5 className="font-medium text-white">Email</h5>
            </div>

            <div className="col-span-2">
              <h5 className="font-medium text-white">Team</h5>
            </div>

            <div className="col-span-2">
              <h5 className="font-medium text-white">Role</h5>
            </div>

            <div className="col-span-1">
              <h5 className="font-medium text-white">Status</h5>
            </div>

            <div className="col-span-1">
              <h5 className="text-right font-medium text-white">Edit</h5>
            </div>
          </div>
          {/* table header end */}

          {/* table body start */}
          <div className="rounded-b-[10px] bg-white dark:bg-boxdark">
            {userData.map((item, index) => (
              <div
                key={index}
                className="grid grid-cols-12 border-t border-[#EEEEEE] px-5 py-4 dark:border-strokedark lg:px-7.5 2xl:px-11"
              >
                <div className="col-span-2">
                  <p className="text-[#637381] dark:text-bodydark flex items-center gap-2">
                    {item.first_name} {item.last_name}
                    {user?.id == item.id && (
                      <span className="rounded-full h-4 w-4 bg-emerald-500 animate-pulse"></span>
                    )}
                  </p>
                </div>

                <div className="col-span-3">
                  <p className="text-[#637381] dark:text-bodydark">
                    {item.email}
                  </p>
                </div>

                <div className="col-span-2">
                  <p className="text-[#637381] dark:text-bodydark">
                    {item.team.name}
                  </p>
                </div>

                <div className="col-span-2">
                  <p className="text-[#637381] dark:text-bodydark">
                    {item.role.name}
                  </p>
                </div>

                <div className="col-span-1">
                  {item.email_verified == null ? (
                    <p className="text-red-800 bg-red-200 text-center rounded-full">
                      Pending
                    </p>
                  ) : (
                    <p className="text-emerald-800 bg-emerald-200 text-center rounded-full">
                      Verified
                    </p>
                  )}
                </div>

                <div className="relative col-span-1">
                  <ActionDropdown
                    classes={
                      index < 2
                        ? "top-full mt-1"
                        : index >= userData.length - 2
                        ? "bottom-full mb-1"
                        : ""
                    }
                    user={item}
                    token={user?.access_token as string}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>


      <SessionExpiredModel 
        tokenExpired = { tokenExpired }
        setTokenExpired =  { setTokenExpired }
        loading={ mutation.isPending }   
      />
    </>
  );
};

export default TableUser;
