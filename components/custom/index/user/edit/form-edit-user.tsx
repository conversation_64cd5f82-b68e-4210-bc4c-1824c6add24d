"use client";
import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";
import { ChevronDown, Mail, User } from "lucide-react";
import { useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/ui/button";
import { zodResolver } from "@hookform/resolvers/zod";
import { FormInput } from "@/components/ui/form/form-input";
import { FormInputDropdown } from "@/components/ui/form/form-input-dropdown";
import {
  Building,
  Customer,
  Organization,
  Product,
  Role,
  Team,
} from "@prisma/client";
import { useCurrentUserToken } from "@/hooks/use-current-user";
import SuccessModel from "@/components/custom/success-model";
import {
  getBuildingData,
  getCustomersData,
  getOrganizationData,
  getProductData,
  getRoleData,
  getTeamData,
} from "../../invite/form-invite-data";
import { editUser, userDetails } from "@/services/api";
import {
  EditUserFormData,
  EditUserSchema,
} from "@/schemas/auth/edit-user/schema";
import { useRouter } from "next/navigation";

interface EditUserFormProps {
  userId: string;
  loading: Boolean;
  tokenExpired: Boolean;
  setLoading: (arg: boolean) => void;
  setTokenExpired: (arg: boolean) => void;
}

const EditUserForm = ({
  userId,
  tokenExpired,
  loading,
  setLoading,
  setTokenExpired,
}: EditUserFormProps) => {
  const router = useRouter();
  const token = useCurrentUserToken();
  const [roles, setRoles] = useState<Role[]>([]);
  const [teams, setTeams] = useState<Team[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [user, setUser] = useState<UserDetailsResponse>();
  const [building, setBuilding] = useState<Building[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [organization, setOrganization] = useState<Organization[]>([]);

  const [modalOpen, setModalOpen] = useState(false);

  const getUserDetails = async () => {
    setLoading(true);
    const record = (await userDetails(
      null,
      userId as string
    )) as APIResponse;
    setLoading(false);
    if (record.status === 0 && record.message == "Token has expired!") {
      setTokenExpired(true);
      return;
    }
    const data: UserDetailsResponse = record.data;
    setUser(data);
    reset(data);
  };

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
    watch,
    reset,
  } = useForm<EditUserFormData>({
    resolver: zodResolver(EditUserSchema),
    defaultValues: {
      product_id: 0,
    },
  });

  const onSubmit = async (values: EditUserFormData) => {
    console.log(values);
    setLoading(true);
    let response = (await editUser(values, userId)) as APIResponse;
    console.log(response);
    if (response.status == 1) {
      reset();
      setModalOpen(true);
    }
    setLoading(false);
  };

  useEffect(() => {
    if (watch("product_id") == 3) {
      loadOrganizationData();
    }
  }, [watch("product_id")]);

  useEffect(() => {
    let id = watch("organization_id") as number;
    if (id > 0) {
      loadBuildingData();
    }
  }, [watch("organization_id")]);

  const onConfirm = () => {
    console.log("Reload now!!!");
    router.refresh();
  };

  const loadData = async () => {
    setLoading(true);
    const roleData = await getRoleData();
    setRoles(roleData);

    const teamsData = await getTeamData();
    setTeams(teamsData);

    const productsData = await getProductData();
    setProducts(productsData);

    const customersData = await getCustomersData({});
    setCustomers(customersData);

    setLoading(false);
  };

  const loadOrganizationData = async () => {
    const organizationData = await getOrganizationData();
    setOrganization(organizationData);
  };

  const loadBuildingData = async () => {
    const buildingData = await getBuildingData(
      watch("organization_id") as number
    );
    setBuilding(buildingData);
  };

  useEffect(() => {
    loadData();
  }, []);
  useEffect(() => {
    getUserDetails();
  }, [reset]);

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)} method="POST">
        <div className="grid grid-cols-2 gap-4">
          <div className="relative mb-6">
            <FormInput
              className=""
              type="text"
              id="first_name"
              placeholder="<EMAIL>"
              defaultValue={user?.first_name}
              label="First Name"
              error={errors.first_name}
              {...register("first_name")}
            />
            <span className="absolute right-4 top-15 text-black/50">
              <User className="h-5 w-5" />
            </span>
          </div>

          <div className="relative mb-6">
            <FormInput
              className=""
              type="text"
              id="last_name"
              placeholder="<EMAIL>"
              defaultValue={user?.last_name}
              label="Last Name"
              error={errors.last_name}
              {...register("last_name")}
              disabled={false}
            />
            <span className="absolute right-4 top-15 text-black/50">
              <User className="h-5 w-5" />
            </span>
          </div>
        </div>

        <div className="relative mb-6">
          <FormInput
            className=""
            type="email"
            id="email"
            placeholder="<EMAIL>"
            defaultValue={user?.email}
            label="Email"
            error={errors.email}
            {...register("email")}
          />
          <span className="absolute right-4 top-15 text-black/50">
            <Mail className="h-5 w-5" />
          </span>
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div className="relative mb-6">
            <FormInputDropdown
              className=""
              id="role_id"
              label="Role"
              defaultValue={user?.role.id}
              error={errors.role_id}
              {...register("role_id", { valueAsNumber: true })}
            >
              <option value={0}>Choose a role</option>
              {roles &&
                roles.map((role) => {
                  return (
                    <option
                      key={role.id}
                      value={role.id}
                      selected={user?.role.id == role.id}
                    >
                      {role.name}
                    </option>
                  );
                })}
            </FormInputDropdown>
            <span className="absolute right-4 top-11 text-black/50">
              <ChevronDown className="h-5 w-5" />
            </span>
          </div>
          <div className="relative mb-6">
            <FormInputDropdown
              className=""
              id="team_id"
              label="Team"
              error={errors.team_id}
              defaultValue={user?.team.id}
              {...register("team_id", { valueAsNumber: true })}
            >
              <option value={0}>Choose a team</option>
              {teams &&
                teams.map((team) => {
                  return (
                    <option
                      key={team.id}
                      value={team.id}
                      selected={user?.team.id == team.id}
                    >
                      {team.name}
                    </option>
                  );
                })}
            </FormInputDropdown>
            <span className="absolute right-4 top-11 text-black/50">
              <ChevronDown className="h-5 w-5" />
            </span>
          </div>
        </div>

        {watch("team_id") > 1 && user?.userToProduct && (
          <div className="relative mb-6">
            <FormInputDropdown
              className=""
              id="product_id"
              label="Product"
              defaultValue={user?.userToProduct[0]?.product?.id}
              error={errors.product_id}
              {...register("product_id", { valueAsNumber: true })}
            >
              <option value={0}>Choose a product</option>
              {products &&
                products.map((product) => {
                  return (
                    <option
                      key={product.id}
                      value={product.id}
                      selected={
                        user?.userToProduct?.find(
                          (item) => item.product?.id == product.id
                        ) != null
                      }
                    >
                      {product.name}
                    </option>
                  );
                })}
            </FormInputDropdown>
            <span className="absolute right-4 top-11 text-black/50">
              <ChevronDown className="h-5 w-5" />
            </span>
          </div>
        )}

        {((user?.userToProduct && watch("product_id") == 1) ||
          (user?.userToProduct &&
            user?.userToProduct[0]?.product?.id == 1)) && (
          <div className="relative mb-6">
            <FormInputDropdown
              className=""
              id="client_id"
              label="Client"
              error={errors.customer_id}
              defaultValue={user?.userToProduct[0]?.product?.id ?? 0}
              {...register("customer_id", { valueAsNumber: true })}
            >
              <option value={0}>Choose a customer</option>
              {customers &&
                customers.map((customer) => {
                  return (
                    <option key={customer.id} value={customer.id}>
                      {customer.name}
                    </option>
                  );
                })}
            </FormInputDropdown>
            <span className="absolute right-4 top-11 text-black/50">
              <ChevronDown className="h-5 w-5" />
            </span>
          </div>
        )}

        {watch("product_id") == 3 && (
          <div className="relative mb-6">
            <FormInputDropdown
              className=""
              id="organization_id"
              label="Organization"
              error={errors.organization_id}
              defaultValue={
                user?.userToBuilding?.flatMap(
                  (item) => item.building?.organization?.id
                ) ?? 0
              }
              {...register("organization_id", { valueAsNumber: true })}
            >
              <option value={0}>Choose a organization</option>
              {organization &&
                organization.map((org) => {
                  return (
                    <option
                      key={org.id}
                      value={org.id}
                      selected={
                        user?.userToBuilding?.find(
                          (item) => item.building?.organization?.id == org.id
                        ) != null
                      }
                    >
                      {org.name}
                    </option>
                  );
                })}
            </FormInputDropdown>
            <span className="absolute right-4 top-11 text-black/50">
              <ChevronDown className="h-5 w-5" />
            </span>
          </div>
        )}

        {watch("product_id") == 3 && (
          <div className="relative mb-6">
            <FormInputDropdown
              className=""
              id="building_id"
              label="Building"
              error={errors.building_id}
              defaultValue={
                user?.userToBuilding?.flatMap((item) => item.building?.id) ?? 0
              }
              {...register("building_id", { valueAsNumber: true })}
            >
              <option value={0}>Choose a building</option>
              {building &&
                building.map((item) => {
                  return (
                    <option
                      key={item.id}
                      value={item.id}
                      selected={
                        user?.userToBuilding?.find(
                          (item) => item.building?.id == item.id
                        ) != null
                      }
                    >
                      {item.name}
                    </option>
                  );
                })}
            </FormInputDropdown>
            <span className="absolute right-4 top-11 text-black/50">
              <ChevronDown className="h-5 w-5" />
            </span>
          </div>
        )}

        <div className="mt-12 mb-6">
          <Button type="submit" className="w-full">
            Update
          </Button>
        </div>
      </form>
      <SuccessModel
        modalOpen={modalOpen}
        setModalOpen={setModalOpen}
        onConfirm={onConfirm}
        title={"User information updated!"}
        description={
          "We have changed the user details information and access management."
        }
        buttonTitle={"OK"}
      />
    </>
  );
};

export default EditUserForm;
