"use client";
import MessageModal from "@/components/custom/modal/model-message";
import { removeUser, resendInvite } from "@/services/api";
import { ChevronDown } from "lucide-react";
import { useRouter } from "next/navigation";
import React, { use, useEffect, useRef, useState } from "react";

const ActionDropdown: React.FC<{
  classes: string;
  user: UserDetailsResponse;
  token: string;
}> = ({ classes, user, token }) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [inviteSent, setInviteSent] = useState(false);
  const [userRemoved, setUserRemoved] = useState(false);
  const route = useRouter();

  const trigger = useRef<any>(null);
  const dropdown = useRef<any>(null);

  // close on click outside
  useEffect(() => {
    const clickHandler = ({ target }: MouseEvent) => {
      if (!dropdown.current) return;
      if (
        !dropdownOpen ||
        dropdown.current.contains(target) ||
        trigger.current.contains(target)
      )
        return;
      setDropdownOpen(false);
    };
    document.addEventListener("click", clickHandler);
    return () => document.removeEventListener("click", clickHandler);
  });

  // close if the esc key is pressed
  useEffect(() => {
    const keyHandler = ({ keyCode }: KeyboardEvent) => {
      if (!dropdownOpen || keyCode !== 27) return;
      setDropdownOpen(false);
    };
    document.addEventListener("keydown", keyHandler);
    return () => document.removeEventListener("keydown", keyHandler);
  });

  const onResendInviteClick = async () => {
    try {
      // Call an API to fetch all the users
      const response = (await resendInvite(
        { email: user.email }
      )) as APIResponse;
      if (response.status == 1) {
        setInviteSent(true);
      }
    } catch (error) {
      console.error("Error changing password:", error);
      alert("An error occurred. Please try again later.");
    }
  };

  const onDeleteClick = async () => {
    try {
      const response = (await removeUser(user.id)) as APIResponse;
      if (response.status == 1) {
        route.refresh();
      }
    } catch (error) {
      console.error("Error changing password:", error);
      alert("An error occurred. Please try again later.");
    }
  };

  const onDetailsClick = async () => {
    route.push(`/user/info/${user.id}`);
  };

  const onEditClick = async () => {
    route.push(`/user/info/${user.id}/edit`);
  };

  return (
    <>
      <div>
        <button
          className="float-right inline-flex items-center gap-1.5 rounded-md px-3 py-1.5 text-sm text-black shadow-11 hover:text-primary dark:bg-meta-4 dark:text-white dark:shadow-none"
          ref={trigger}
          onClick={() => setDropdownOpen(!dropdownOpen)}
        >
          Action
          <ChevronDown className="h-4 w-4" />
        </button>
        <div
          ref={dropdown}
          onFocus={() => setDropdownOpen(true)}
          onBlur={() => setDropdownOpen(false)}
          className={`absolute right-0 z-10 w-full max-w-39.5 rounded-[5px] bg-white py-2.5 shadow-12 dark:bg-boxdark ${classes} ${
            dropdownOpen === true ? "block" : "hidden"
          }`}
        >
          {user.email_verified == null && (
            <button
              className="flex w-full px-4 py-2 text-sm hover:bg-whiter hover:text-primary dark:hover:bg-meta-4"
              onClick={onResendInviteClick}
            >
              Re-Invite
            </button>
          )}
          <button
            className="flex w-full px-4 py-2 text-sm hover:bg-whiter hover:text-primary dark:hover:bg-meta-4"
            onClick={onEditClick}
          >
            Edit
          </button>
          <button
            className="flex w-full px-4 py-2 text-sm hover:bg-whiter hover:text-primary dark:hover:bg-meta-4"
            onClick={onDeleteClick}
          >
            Delete
          </button>
          <button
            className="flex w-full px-4 py-2 text-sm hover:bg-whiter hover:text-primary dark:hover:bg-meta-4"
            onClick={onDetailsClick}
          >
            Details
          </button>
        </div>
      </div>
      <MessageModal
        setModalOpen={setInviteSent}
        modalOpen={inviteSent}
        setConfirm={() => {
          setInviteSent(!inviteSent);
        }}
        title={"Invite Resent"}
        description={`We have email ${user.first_name} ${user.last_name} a new invitation link which will be valid for an hour.`}
        buttonTitle={"OK"}
      />
    </>
  );
};

export default ActionDropdown;
