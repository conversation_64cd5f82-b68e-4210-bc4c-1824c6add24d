type BreadcrumbProps = {
  active?: string
  pageName: string
  onClick: (type: string) => void
}
const Breadcrumb = ({ pageName, active = "", onClick }: BreadcrumbProps) => {
  return (
    <div 
      className={`mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between 
        ${active === pageName ? 'bg-primary' : ' bg-secondary'} 
        rounded-full s p-1.5 px-4`}  
        onClick={() => onClick(pageName)}
    >
      <div className="body4 text-neutrals">{ pageName }</div>
    </div>
  );
};

export default Breadcrumb;