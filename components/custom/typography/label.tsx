"use client"
import { cva, VariantProps } from "class-variance-authority";
import React from "react";
import * as LabelPrimitive from "@radix-ui/react-label"
import { cn } from "@/lib/utils";

const labelVariants = cva(
  "font-metropolis", 
  {
    variants: {
      variant: {
        label1:
          "lg:text-[24px] md:text-[20px] sm:text-[16px] leading-[24px]",
        label2:
          "lg:text-[28px] md:text-[20px] sm:text-[18px] leading-[28px]",
        label3:
          "lg:text-[20px] md:text-[18px] sm:text-[16px] leading-[26px]",
        label4:
          "lg:text-[18px] md:text-[16px] sm:text-[14px] leading-[24px]",
        label5:
          "lg:text-[16px] md:text-[12px] sm:text-[10px] leading-[20px]",
        label6:
          "lg:text-[14px] md:text-[10px] sm:text-[8px] leading-[20px]",
      },
      font: {
        regular: "font-normal",
        medium: "font-medium",
        bold: "font-bold",
      },
    },
    defaultVariants: {
      variant: "label1",
      font: "regular",
    },
  }
);


const Label = React.forwardRef<
  React.ElementRef<typeof LabelPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &
    VariantProps<typeof labelVariants>
>(({ className, variant, font, children, ...props }, ref) => (
  <LabelPrimitive.Root
    ref={ref}
    className={cn(labelVariants({className, variant, font}))}
    {...props}
  >
    {children}
  </LabelPrimitive.Root>
))
Label.displayName = LabelPrimitive.Root.displayName

export default Label;
