"use client"
import { cva, VariantProps } from "class-variance-authority";
import React from "react";
import * as LabelPrimitive from "@radix-ui/react-label"
import { cn } from "@/lib/utils";

const bodyVariants = cva(
  "font-metropolis", 
  {
    variants: {
      variant: {
        body1:
          "lg:text-[32px] md:text-[24px] sm:text-[20px] leading-[30px]",
        body2:
          "lg:text-[28px] md:text-[20px] sm:text-[18px] leading-[28px]",
        body3:
          "lg:text-[26px] md:text-[18px] sm:text-[16px] leading-[26px] ",
        body4:
          "lg:text-[24px] md:text-[16px] sm:text-[14px] leading-[24px]",
        body5:
          "lg:text-[20px] md:text-[14px] sm:text-[12px] leading-[22px]",
        body6:
          "lg:text-[16px] md:text-[12px] sm:text-[10px] leading-[20px]",
      },
      font: {
        regular: "font-normal",
        medium: "font-medium",
        bold: "font-bold",
      },
    },
    defaultVariants: {
      variant: "body1",
      font: "regular",
    },
  }
);


const Body = React.forwardRef<
  React.ElementRef<typeof LabelPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &
    VariantProps<typeof bodyVariants>
>(({ className, variant, font, children, ...props }, ref) => (
  <LabelPrimitive.Root
    ref={ref}
    className={cn(bodyVariants({className, variant, font}))}
    {...props}
  >
    {children}
  </LabelPrimitive.Root>
))
Body.displayName = LabelPrimitive.Root.displayName

export default Body;
