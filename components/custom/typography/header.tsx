"use client"
import * as React from "react"
import * as LabelPrimitive from "@radix-ui/react-label"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"


const headerVariants = cva(
  "font-metropolis",
  {
    variants: {
      variant: {
        header1:
          "lg:text-[36px] md:text-[32px]  sm:text-[24px] leading-[40px]",
        header2:
          "lg:text-[30px]  md:text-[28px] sm:text-[20px] leading-[36px]",
        header3:
          "lg:text-[26px] md:text-[24px] sm:text-[18px] leading-[32px] ",
        header4:
          "lg:text-[24px] md:text-[20px] sm:text-[16px] leading-[28px]",
        header5:
          "lg:text-[22px] md:text-[18px] sm:text-[18px] leading-[26px]",
        header6:
          "lg:text-[20px] md:text-[16px] sm:text-[16px] leading-[24px]",
      },
      font: {
        regular: "font-normal",
        medium: "font-medium",
        bold: "font-bold",
      },
    },
    defaultVariants: {
      variant: "header1",
      font: "regular",
    },
  }
)

const Header = React.forwardRef<
  React.ElementRef<typeof LabelPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &
    VariantProps<typeof headerVariants>
>(({ className, variant, font, children, ...props }, ref) => (
  <LabelPrimitive.Root
    ref={ref}
    className={cn(headerVariants({className, variant, font}))}
    {...props}
  >
    {children}
  </LabelPrimitive.Root>
))
Header.displayName = LabelPrimitive.Root.displayName

export default Header;
