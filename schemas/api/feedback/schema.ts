import * as z from "zod";

export const FeedbackSchema = z.object({
  email: z.string({
    required_error: "Please enter your email address!"
  }).email({
    message: "Please enter a valid email address."
  }),

  duration: z.string({
    required_error: "Please enter the duration!"
  }),

  feature: z.string({
    required_error: "Please enter your favourite feature!"
  }),

  improve: z.string({
    required_error: "Please share your suggestions!"
  }),

  experience: z.string({
    required_error: "Please tell us about your experience!"
  }),

  rate: z
    .number({
      required_error: "Please rate the experience!"
    })
    .min(1, { message: "Minimum rating is 1" })
    .max(10, { message: "Maximum rating is 10" })
});

export type FeedbackFormData = z.infer<typeof FeedbackSchema>;
