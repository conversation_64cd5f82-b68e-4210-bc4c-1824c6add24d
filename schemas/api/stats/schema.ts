import * as z from "zod"

export const StatsSchema = z.object({
    event_name: z.string({
        message: "Please enter event name!"
    }),
    event_click: z.optional(z.boolean({
        message: "Please enter if the event is of click type!"
    })),
    result: z.optional(z.any({
        message: "Please enter result!"
    })),
    organization_id: z.optional(z.number({
        required_error: "Please enter the organization!"
    })
    .refine((data) => data != 0, {
        message: "Please enter the organization!"
    })),
    customer_id: z.optional(z.number({
        required_error: "Please enter the customer!"
    })
    .refine((data) => data != 0, {
        message: "Please enter the customer!"
    })),
    user_id: z.optional(z.string({
        required_error: "Please enter the user!"
    }))
})


export type StatsFormData = z.infer<typeof StatsSchema>