import * as z from 'zod';

export const FirebaseSchema = z.object({
    startDate: z.string().optional(), // Optionally a string date
    endDate: z.string().optional(),   // Optionally a string date
    eventName: z.string().optional(), // Optionally a string event name
    metricNames: z.array(z.string()).optional(), // Optionally an array of strings
    dimensionNames: z.array(z.string()).optional(), // Optionally an array of strings
  });
