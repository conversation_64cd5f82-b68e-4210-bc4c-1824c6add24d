import * as z from "zod"

export const UserSchema = z.object({
    first_name: z.string({
        required_error: "Please enter client's first name!"
    }).min(3, {
        message: "Username must be at least 3 characters!"
    }),
    last_name: z.string({
        required_error: "Please enter client's family name!"
    }).min(3, {
        message: "Username must be at least 3 characters!"
    }),
    email: z.string({
        required_error: "Please enter client's email address!"
    })
    .email({ 
        message: "Please enter a valid email address." 
    }),
    role_id: z.number({
        required_error: "Please enter the role!"
    }),
})


// export type UserSchemaFormData = z.infer<typeof UserSchema>