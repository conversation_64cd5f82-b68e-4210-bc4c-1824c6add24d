import * as z from "zod"

export const SignInSchema = z.object({
    username: z.string({
        required_error: "Please enter your username!"
    }).min(3, {
        message: "Username must be at least 3 characters!"
    }),
    password: z.string({
        required_error: "Please enter your password!"
    }).min(8, {
        message: "Password must be at least 3 characters!"
    }),
    code: z.array(z.number()).optional(),
    // code: z.optional(
    //     z.string({
    //         required_error: "Please enter your verification code!"
    //     })
    //     .min(6, {
    //         message: "Verification code must be 6 digits long!"
    //     })
    //     .transform((code) => code.split('').map(Number)),
    // )
})


export type SignInInputFormData = z.infer<typeof SignInSchema>