import * as z from "zod"

export const ChangePasswordSchema = z.object({
    current_password: z.string({
        required_error: "Please enter your current password!"
    }).min(8, {
        message: "Password must be at least 8 characters!"
    }),
    new_password: z.string({
        required_error: "Please enter your new password!"
    }).min(8, {
        message: "Password must be at least 8 characters!"
    }),
    confirm_password: z.string({
        required_error: "Please confirm your password!"
    }).min(8, {
        message: "Password must be at least 8 characters!"
    }),
})
.refine((data) => data.new_password === data.confirm_password, {
    message: 'Passwords does not match!',
    path: ['confirm_password'],
})


export type ChangePasswordFormData = z.infer<typeof ChangePasswordSchema>