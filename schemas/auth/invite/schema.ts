import * as z from "zod"

export const InviteSchema = z.object({
    first_name: z.string({
        required_error: "Please enter first name!",
    }).min(3, {
        message: "First name must be at least 3 characters!"
    }).trim(),
    last_name: z.string({
        required_error: "Please enter family name!"
    }).min(3, {
        message: "Last name must be at least 3 characters!"
    }).trim(), 
    email: z.string({
        required_error: "Please enter client's email address!"
    })
    .email({ 
        message: "Please enter a valid email address." 
    }),
    role_id: z.number({
        required_error: "Please select the role!"
    })
    .refine((data) => data != 0, {
        message: "Please select the role!"
    }),
    team_id: z.number({
        required_error: "Please select the team!"
    })
    .refine((data) => data != 0, {
        message: "Please select the team!"
    }),
    product_id:  z.optional(z.number({
        required_error: "Please enter the product!"
    })
    .refine((data) => data != 0, {
        message: "Please select the product!"
    })),
    customer_id:  z.optional(z.number({
        required_error: "Please enter the client!"
    })
    .refine((data) => data != 0, {
        message: "Please select the client!"
    })),
    organization_id: z.optional(z.number({
        required_error: "Please enter the organization!"
    })
    .refine((data) => data != 0, {
        message: "Please select the organization!"
    })),
    building_id: z.optional(z.number({
        required_error: "Please enter the building!"
    })
    .refine((data) => data != 0, {
        message: "Please select the building!"
    })),
    full_access: z.boolean().default(false),
})


export type InviteFormData = z.infer<typeof InviteSchema>