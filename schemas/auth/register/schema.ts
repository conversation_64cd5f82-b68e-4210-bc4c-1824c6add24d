import * as z from "zod";

export const RegisterSchema = z.object({
  name: z.string({
    required_error: "Please enter your full name!"
  }).min(2, {
    message: "Name must be at least 2 characters!"
  }),

  email: z.string({
    required_error: "Please enter your email!"
  }).email({
    message: "Please enter a valid email address!"
  }),

  username: z.string({
    required_error: "Please choose a username!"
  }).min(3, {
    message: "Username must be at least 3 characters!"
  }),

  password: z.string({
    required_error: "Please create a password!"
  }).min(8, {
    message: "Password must be at least 8 characters!"
  }),

  role: z.string().optional()
});

export type RegisterInputFormData = z.infer<typeof RegisterSchema>;
