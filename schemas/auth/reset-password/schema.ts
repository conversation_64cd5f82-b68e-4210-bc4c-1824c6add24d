import * as z from "zod"

export const ResetPasswordSchema = z.object({
    code: z.string({
        required_error: "Please enter your verification code!"
    })
    .min(6, {
        message: "Verification code must be 6 digits long!"
    }),
    // .transform((code) => code.split('').map(Number)),
    password: z.string({
        required_error: "Please enter your password!"
    }).min(8, {
        message: "Password must be at least 8 characters!"
    }),
    confirmPassword: z.string({
        required_error: "Please confirm your password!"
    }).min(8, {
        message: "Username must be at least 8 characters!"
    })
})
.refine((data) => data.password === data.confirmPassword, {
    path: ['confirmPassword'],
    message: 'Passwords does not match'
})


export type ResetPasswordFormData = z.infer<typeof ResetPasswordSchema>