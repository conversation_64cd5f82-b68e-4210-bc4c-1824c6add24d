import * as z from "zod"

export const InviteAcceptanceSchema = z.object({
    username: z.string({
        required_error: "Please enter your username!"
    }).min(3, {
        message: "Username must be at least 3 characters!"
    }),
    password: z.string({
        required_error: "Please enter your new password!"
    }).min(8, {
        message: "Password must be at least 8 characters!"
    }),
    confirm_password: z.string({
        required_error: "Please confirm your password!"
    }).min(8, {
        message: "Password must be at least 8 characters!"
    }),
    email_verified: z.boolean().default(true)
})
.refine((data) => data.password === data.confirm_password, {
    path: ['confirm_password'],
    message: 'Passwords does not match'
})


export type InviteAcceptanceFormData = z.infer<typeof InviteAcceptanceSchema>