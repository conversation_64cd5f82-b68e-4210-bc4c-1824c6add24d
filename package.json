{"name": "lg_portal_admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@google-analytics/data": "^5.1.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.76.0", "@tanstack/react-query-devtools": "^5.76.1", "@types/js-cookie": "^3.0.6", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "flatpickr": "^4.6.13", "js-cookie": "^3.0.5", "lucide-react": "^0.510.0", "next": "15.3.2", "next-auth": "^4.24.11", "next-client-cookies": "^2.1.0", "papaparse": "^5.5.3", "puppeteer": "^24.9.0", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "tailwind-merge": "^3.3.0", "zod": "^3.25.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.2", "postcss": "^8.5.3", "prisma": "^6.8.2", "tailwindcss": "^4.1.6", "typescript": "^5"}}