import { BetaAnalyticsDataClient } from "@google-analytics/data";

const propertyId = process.env.GA_PROPERTY_ID;
const analyticsDataClient = new BetaAnalyticsDataClient({
  credentials: {
    client_email: process.env.GA_CLIENT_EMAIL,
    private_key: process.env.GA_PRIVATE_KEY?.replace(/\n/gm, "\n"), // replacing is necessary
  },
});

export interface GetParams {
  eventName?: string;
  startDate?: string;
  endDate?: string;
  metricNames?: string[];
  dimensionNames?: string[];
  filters?: Record<string, string>;
}

export interface MapAndroidToIos {
  [key: string]: string;
}

export const mapAndroidToIos: MapAndroidToIos = {
  TimerActivity: 'TimerView',
  MainActivity: 'FeedView',
  SplashActivity: 'SplashView',
  ProfileActivity: 'ProfileView',
  WelcomeActivity: 'WelcomeView',
  SNMActivity: 'SNMView',
  EmergencyInfoFlowActivity: 'EmergencyInfoView',
  EmergencyActivity: 'EmergencyView',
  GuideActivity: 'GuideView',
  WebViewActivity: 'ResourcesView',
  AddressActivity: 'CreateChatAccountView',
  CategoriesActivity: 'CategoriesView',
  EditProfileActivity: 'EditMyProfileView',
  ThemeActivity: 'ThemeView',
  FeedNotificationActivity: 'FeedNotificationView',
};

export async function fetchReport({
  eventName = '',
  startDate,
  endDate,
  metricNames = ['eventCount'],
  dimensionNames = ['eventName'],
  filters = {}, // Default filters to an empty object
}: GetParams) {
  // Set dynamic default values for startDate and endDate
  const today = new Date();
  const defaultEndDate = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
  const defaultStartDate = new Date(today);
  defaultStartDate.setDate(today.getDate() - 30);
  const formattedDefaultStartDate = `${defaultStartDate.getFullYear()}-${String(defaultStartDate.getMonth() + 1).padStart(2, '0')}-${String(defaultStartDate.getDate()).padStart(2, '0')}`;

  const finalStartDate = startDate || formattedDefaultStartDate;
  const finalEndDate = endDate || defaultEndDate;

  // Conditionally set the dimensionFilter only if eventName is provided
  const dimensionFilter = eventName
    ? {
        filter: {
          fieldName: 'eventName',
          stringFilter: {
            value: eventName,
          },
        },
      }
    : null;

  // Incorporate additional filters such as region
  const additionalFilters = Object.keys(filters).length
    ? {
        filter: {
          fieldName: Object.keys(filters)[0], // Get the first filter key
          stringFilter: {
            value: filters[Object.keys(filters)[0]], // Get the corresponding value
          },
        },
      }
    : null;

  const dimensions = dimensionNames.map((dimensionName) => ({
    name: dimensionName,
  }));

  console.log(`eventName: ${eventName}`);
  console.log(`startDate: ${finalStartDate}`);
  console.log(`endDate: ${finalEndDate}`);
  

  let response;
  try {
    // Construct the requestParams with optional dimensionFilter and additionalFilters
    const requestParams = {
      property: `properties/${propertyId}`,
      dateRanges: [
        {
          startDate: finalStartDate,
          endDate: finalEndDate,
        },
      ],
      dimensions: dimensions,
      metrics: metricNames.map((metricName) => ({ name: metricName })),
      ...(dimensionFilter && { dimensionFilter }), // Conditionally include dimensionFilter if it's defined
      ...(additionalFilters && { dimensionFilter: additionalFilters }), // Include additional filters
    };

    [response] = await analyticsDataClient.runReport(requestParams);
  } catch (error) {
    console.error('Error running report:', error);
    throw new Error('Error running report');
  }

  console.log(response);

  const cleanedData = (response.rows ?? []).reduce((acc, row) => {
    const dimensionValue = row?.dimensionValues?.[0]?.value ?? '';
    const metricValues = row?.metricValues?.map((metric) => metric.value) ?? [];
    return { ...acc, [dimensionValue]: metricValues };
  }, {});

  return cleanedData;
}

export function combineResponses(responses: any[], mapAndroidToIos: MapAndroidToIos) {
  let combinedResponse: { [key: string]: string[] } = responses.reduce(
    (acc: any, response: any) => ({ ...acc, ...response }),
    {}
  );

  for (const androidName in mapAndroidToIos) {
    const iosName = mapAndroidToIos[androidName];
    const commonName = androidName.replace('Activity', '');

    if (combinedResponse[androidName] && combinedResponse[iosName]) {
      const androidValues = combinedResponse[androidName].map(Number);
      const iosValues = combinedResponse[iosName].map(Number);

      const maxLength = Math.max(androidValues.length, iosValues.length);
      const sumValues = Array(maxLength).fill(0);

      for (let i = 0; i < maxLength; i++) {
        sumValues[i] = (androidValues[i] || 0) + (iosValues[i] || 0);
      }

      combinedResponse[commonName] = sumValues.map(String);
    }
  }

  return combinedResponse;
}


export const setDataToZeroByPrefixes = (combinedResponse: any, prefixes: string[]) => {
  // Ensure valid combined response
  if (!combinedResponse) {
    return combinedResponse;
  }

  // Iterate through the keys in the combined response object
  for (const name in combinedResponse) {
    if (combinedResponse.hasOwnProperty(name)) {

      // Check if the name starts with any of the provided prefixes
      const matchesPrefix = prefixes.some(prefix => name.startsWith(prefix));

      if (matchesPrefix) {

        // Check if the value is an array
        if (Array.isArray(combinedResponse[name])) {
          // Set all numeric entries in the array to '0'
          combinedResponse[name] = combinedResponse[name].map((value: number) =>
            isNaN(value) ? value : '0'
          );
        } else {
          // If it's a single value, check if it's numeric and set to '0'
          if (!isNaN(combinedResponse[name])) {
            combinedResponse[name] = '0';
          }
        }
      }
    }
  }

  return combinedResponse;
};