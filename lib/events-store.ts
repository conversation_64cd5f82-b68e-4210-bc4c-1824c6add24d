/**
 *
 * @author(<PERSON><PERSON><PERSON><PERSON>)
 * @description(Please stick to the same event structure as soon belong. If you want to add new events, please talk to the team before doing so.)
 *  
 * @example(CLICK_EVENT)
 * @argument {"event_name": "EVENT_REPORT_DOWNLOAD", "event_click": 1, "customer_id": 1}
 * 
 * 
 * @example(PAGE_VISIT_EVENT)
 * @argument {"event_name": "EVENT_PAGE_VISIT", "result": "{\"product\": \"connect\", \"page\": \"dashboard\" }", "customer_id": 1}
 * 
 * 
 * @example(OTHER_EVENT)
 * @argument {"EVENT_FEEDBACK_SEND": "", "result": "{\"product\": \"connect\", \"sent\": 1" }", "organization_id": 1}
 * 
*/


export const EVENT_REPORT_DOWNLOAD = 'event_report_download_click'
export const EVENT_PAGE_VISIT = 'event_page_visit'
export const EVENT_SESSION_START = 'event_session_start'
export const EVENT_SESSION_END = 'event_session_end'

export const EVENT_SIDEMENU_FEEDBACK= 'event_feedback_click'
export const EVENT_SIDEMENU_CONTACT= 'event_contact_click'
export const EVENT_SIDEMENU_FAQ= 'event_faq_click'
export const EVENT_SIDEMENU_MY_PROFILE= 'event_my_profile_click'
export const EVENT_SIDEMENU_CHANGE_PASSWORD= 'event_change_password_click'
export const EVENT_SIDEMENU_LOGOUT= 'event_logout_click'

export const EVENT_FEEDBACK_SEND= 'event_feedback'
export const EVENT_CONTACT_SEND= 'event_contact'
export const EVENT_CHANGE_PASSWORD= 'event_change_password'
