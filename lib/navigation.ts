'use client';

import { useRouter } from 'next/navigation';
import { ACCESS_TOKEN, getArray<PERSON>ookie, getCookie, LAST_ACTIVE_DASHBOARD, setCookie, USER_PERMISSIONS } from '@/lib/cookie-store';

export const useSecureRouter = () => {
  const router = useRouter();

  const securePush = (path: string, extraParams: Record<string, string> = {}) => {
    const token = getCookie(ACCESS_TOKEN);
    const user_permission = getArrayCookie(USER_PERMISSIONS);
    if(!getCookie(LAST_ACTIVE_DASHBOARD) && user_permission.length > 0){
      setCookie(LAST_ACTIVE_DASHBOARD, user_permission[0], 2592000);
    }
    const permission = getCookie(LAST_ACTIVE_DASHBOARD);

    const params = new URLSearchParams({
      ...(token ? { token } : {}),
      ...(user_permission[0] ? { permission } : {}),
      ...extraParams,
    });

    router.push(`${path}?${params.toString()}`);
  };

  return { securePush };
};