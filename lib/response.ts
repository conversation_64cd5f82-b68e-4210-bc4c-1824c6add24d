import { NextResponse } from "next/server"

export function ResponseSuccess(message: string, data?: any){
    return NextResponse.json({
        status: 1, 
        message: message,
        data: data
    }, {
        status: 200,
    })
}


export function ResponseSuccessRedirect(message: string, ...data: any){
    return NextResponse.json({
        status: 1, 
        message: message,
        data
    }, {
        status: 200,
    })
}

export function ResponseError(message: string, error?: any){
    return NextResponse.json({
        status: 0, 
        message: message,
        error: error
    }, {
        status: 200,
    })
}