import { useCookies } from 'next-client-cookies';
import { GetUserDetail } from "@/utils/api";
import { USER_NAME, EMAIL, ROLE_NAME, TEAM_NAME, USER_ID, ACCESS_TOKEN } from '@/lib/cookie-store';
import { string } from 'zod';

export const checkCookie= async (cookies: ReturnType<typeof useCookies>, token: string, user_id: string, setLoading: (loading: boolean) => void) => {
    const userName = cookies.get(USER_NAME);
    const email = cookies.get(EMAIL);
    const roleName = cookies.get(ROLE_NAME);
    const teamName = cookies.get(TEAM_NAME);
    
  
    if (!userName || !email || !roleName || !teamName) {
      try {
        setLoading(true);
        const response = await GetUserDetail(user_id);
  
        if (response.code === 1) {
          const user = response.data;
  
          // Store user details in cookies
          cookies.set(USER_NAME, user?.name, { path: '/' });
          cookies.set(EMAIL, user.email, { path: '/' });
          //cookies.set(ROLE_NAME, user.role.name, { path: '/' });
        } else {
          console.error("Failed to fetch user details");
        }
      } catch (error) {
        console.error("Error fetching user details:", error);
      } finally {
        setLoading(false);
      }
    }
  };