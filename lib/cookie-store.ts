import Cookies from 'js-cookie';

export const ACCESS_TOKEN = 'lg_access_token';
export const REFRESH_TOKEN = 'lg_refresh_token';
export const USER_ID = 'lg_user_id';
export const USER_NAME = 'lg_user_name';
export const USER_ROLES = 'lg_user_roles';
export const USER_PERMISSIONS = 'lg_user_permission';
export const LAST_ACTIVE_DASHBOARD = 'lg_last_active_dashboard';
export const EMAIL = 'email';
export const APP_CUSTOMER = 'lg_pp_app_customer'
export const APP_CUSTOMER_ID = 'lg_pp_app_customer_id'
export const ROLE_NAME = 'role_name';
export const TEAM_NAME = 'team_name';

/**
 * Set a cookie with dynamic expiry in seconds.
 * @param key - The cookie name.
 * @param value - The data to store (object, array, string, etc.).
 * @param expirySeconds - Expiry duration in seconds.
 */
export const setCookie = (key: string, value: string, days = 1) => {
  Cookies.set(key, value, {
    expires: days/24,
    sameSite: 'Lax',
    secure: process.env.NODE_ENV === 'production',
  });
};

export const getCookie = (key: string): string | undefined => {
  return Cookies.get(key);
};

export const removeCookie = (key: string) => {
  Cookies.remove(key);
};

export const setArrayCookie = (key: string, array: any[], expirySeconds: number = 3600) => {
  const expiryDate = new Date(new Date().getTime() + expirySeconds * 1000);
  Cookies.set(key, JSON.stringify(array), {
    expires: expiryDate,
    sameSite: 'Lax',
    secure: process.env.NODE_ENV === 'production',
  });
};

export const getArrayCookie = (key: string): any[] => {
  const cookieValue = Cookies.get(key);
  if (!cookieValue) return [];

  try {
    return JSON.parse(cookieValue);
  } catch (error) {
    console.error('Error parsing cookie:', error);
    return [];
  }
};

/**
 * Set a number value in a cookie.
 * @param key - The cookie name
 * @param value - The number to store
 * @param expirySeconds - Expiry time in seconds
 */
export const setNumberCookie = (key: string, value: number, expirySeconds: number) => {
  const expiryDate = new Date(Date.now() + expirySeconds * 1000);

  Cookies.set(key, value.toString(), {
    expires: expiryDate,
    sameSite: 'Lax',
    secure: process.env.NODE_ENV === 'production',
  });
};

export const getNumberCookie = (key: string): number | null => {
  const val = Cookies.get(key);
  return val ? parseInt(val, 10) : null;
};