import { Palette } from "@/types/color-palette"
  
export type THEME = {
    textColor: string
    iconBgColor: string
    iconTextColor: string
}

export const ColorPalette = [
  `var(--color-lightTeal)`, 
  `var(--color-mutedTeal)`, 
  `var(--color-blueTeal)`, 
  `var(--color-oceanBlue)`,
  `var(--color-blueViolet)`, 
  `var(--color-midnightBlue)`, 
  `var(--color-deepTeal)`]

export const ColorPaletteWithBackground: Palette[] = [
  {
    color:`var(--color-lightTeal)`,
    hover:`var(--color-lightTealHover)`,
    background:`var(--color-lightTealBg)`
  },
  {
    color:`var(--color-mutedTeal)`,
    hover:`var(--color-mutedTealHover)`,
    background:`var(--color-mutedTealBg)`
  },
  {
    color:`var(--color-blueTeal)`,
    hover:`var(--color-blueTealHover)`,
    background:`var(--color-blueTealBg)`
  },
  {
    color:`var(--color-oceanBlue)`,
    hover:`var(--color-oceanBlueHover)`,
    background:`var(--color-oceanBlueBg)`
  },
  {
    color:`var(--color-blueViolet)`,
    hover:`var(--color-blueVioletHover)`,
    background:`var(--color-blueVioletBg)`
  },
  {
    color:`var(--color-midnightBlue)`,
    hover:`var(--color-midnightBlueHover)`,
    background:`var(--color-midnightBlueBg)`
  },
  {
    color:`var(--color-deepTeal)`,
    hover:`var(--color-deepTealHover)`,
    background:`var(--color-deepTealBg)`
  },
  
]