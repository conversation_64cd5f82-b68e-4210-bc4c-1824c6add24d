import React, { ReactNode, createContext, useContext, useEffect, useState } from 'react';
import { DateProvider, DateContext } from '@/contexts/date-context'
import { useCookies } from 'next-client-cookies';
import { getCookie, APP_CUSTOMER, APP_CUSTOMER_ID } from '@/lib/cookie-store'
const FirebaseContext = createContext<any>(null);


interface FirebaseProviderProps {
  children: ReactNode;
}

export function FirebaseProvider({ children }: FirebaseProviderProps) {
  
  const { date } = useContext(DateContext);
  const startDate = date?.start;
  const endDate = date?.end;
  const customer = getCookie(APP_CUSTOMER)as string ?? '';
 

  console.log('startDate:', startDate);
  console.log('endDate:', endDate);
  console.log('customer:', customer);

  const [data, setData] = useState<any[]>([]);

  useEffect(() => {
    async function fetchData() {
      let url = '';

      // Determine which API to use based on the customer value
      switch (customer) {
        case 'BCEHS':
          url = '/api/v1/firebase-bc';
          break;
        case 'Northwest Ontario':
          url = '/api/v1/firebase-on';
          break;
        case 'Metis':
          url = '/api/v1/firebase-metis';
          break;
          default:
            // Use default API if no customer or unrecognized customer
            console.warn('No customer or unrecognized customer, using default API.');
            url = '/api/v1/firebase';
            break;
      }

      const body = JSON.stringify({ startDate, endDate });

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: body,
      });

      if (!response.ok) {
        console.error('Error fetching data:', response.statusText);
        return;
      }

      const result = await response.json();
      console.log('fetchData result:', result);
      setData([result]);
    }

    if (startDate && endDate) {
      fetchData();
    }

  }, [startDate, endDate, customer]);

  return (
    <FirebaseContext.Provider value={data}>
      {children}
    </FirebaseContext.Provider>
  );
}

export function useFirebaseData() {
  const context = useContext(FirebaseContext);
  if (context === undefined) {
    throw new Error('useFirebaseData must be used within a FirebaseProvider');
  }
  return context;
}