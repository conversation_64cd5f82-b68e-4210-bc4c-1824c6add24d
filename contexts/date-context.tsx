import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface DateRange {
  start: string;
  end: string;
}

interface DateContextProps {
  date: DateRange;
  setDate: (date: DateRange) => void;
}

// Provide a default value to avoid undefined checks
export const DateContext = createContext<DateContextProps>({
  date: { start: '', end: '' },
  setDate: () => {}, // Empty function as default
});

export const DateProvider = ({ children }: { children: ReactNode }) => {
  const [date, setDate] = useState<DateRange>({ start: '', end: '' });

  useEffect(() => {
    const today = new Date();
    const thirtyDaysAgo = new Date(today);
    thirtyDaysAgo.setDate(today.getDate() - 30);

    const format = (date: Date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };

    setDate({
      start: format(thirtyDaysAgo),
      end: format(today),
    });
  }, []);

  return (
    <DateContext.Provider value={{ date, setDate }}>
      {children}
    </DateContext.Provider>
  );
};

// Use this in components to directly access `date` and `setDate` without undefined checks
export const useDateContext = () => useContext(DateContext);
