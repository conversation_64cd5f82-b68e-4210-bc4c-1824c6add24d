import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import <PERSON> from 'papaparse';
import { DateContext } from './date-context';

type CsvDataType = {
    [key: string]: string | number;
};

interface CsvProviderProps {
    csvUrl: string;
    children: ReactNode;
}

const CsvContext = createContext<{ data: CsvDataType[]; loading: boolean; error: Error | null }>({
    data: [],
    loading: true,
    error: null,
});

export const CsvProvider: React.FC<CsvProviderProps> = ({ csvUrl, children }) => {
    const { date } = useContext(DateContext);
    const [data, setData] = useState<CsvDataType[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<Error | null>(null);

    useEffect(() => {
        const fetchData = async () => {
            try {
                console.log(`Fetching CSV data from: ${csvUrl}`);
                const response = await fetch(csvUrl);
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }

                if (response.body) {
                    const reader = response.body.getReader();
                    const result = await reader.read();
                    const decoder = new TextDecoder('utf-8');
                    const csv = decoder.decode(result.value);
                    
                    
                    Papa.parse(csv, {
                        header: true,
                        dynamicTyping: true,
                        skipEmptyLines: true,
                        complete: (results) => {
                            
                            setData(results.data as CsvDataType[]);
                            setLoading(false);
                        },
                        error: (err: any) => {
                            
                            setError(err);
                            setLoading(false);
                        },
                    });
                } else {
                    throw new Error('Response body is null');
                }
            } catch (err) {
                console.error('Error fetching CSV:', err);
                setError(err as Error);
                setLoading(false);
            }
        };

        fetchData();
    }, [csvUrl, date]); // Refetch data when the date changes

    return (
        <CsvContext.Provider value={{ data, loading, error }}>
            {children}
        </CsvContext.Provider>
    );
};

export const useCsvData = () => {
    const context = useContext(CsvContext);
    if (context === undefined) {
        throw new Error('useCsvData must be used within a CsvProvider');
    }
    return context;
};